// استيراد هوكس React الأساسية لإنشاء السياق وإدارة الحالة
import { createContext, useState, useContext, useEffect } from 'react';
// استيراد دوال Sentry لتتبع المستخدمين والأخطاء
import { setUserContext, clearUserContext, captureErrorWithContext } from '../config/sentry';
// استيراد دالة لإجراء طلبات API مصادق عليها
import { authenticatedGet } from '../utils/api';

// إنشاء سياق المصادقة (AuthContext)
// سيتم استخدام هذا السياق لتوفير حالة المصادقة ووظائفها لجميع المكونات الفرعية.
export const AuthContext = createContext(null);

/**
 * هوك مخصص (Custom Hook) `useAuth`.
 * يوفر طريقة سهلة للوصول إلى سياق المصادقة من أي مكون وظيفي.
 * 
 * @returns {object} - كائن يحتوي على حالة المستخدم، حالة التحميل، ودوال المصادقة.
 * @throws {Error} - يرمي خطأ إذا لم يتم استخدام الهوك داخل مكون `AuthProvider`.
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  // التحقق للتأكد من أن الهوك يستخدم داخل مزود السياق
  if (!context) {
    throw new Error('يجب استخدام useAuth داخل مكون AuthProvider');
  }
  return context;
};

/**
 * مكون `AuthProvider`.
 * يوفر سياق المصادقة لجميع المكونات الفرعية.
 * يدير حالة المستخدم (مسجل دخول أم لا)، وحالة التحميل، ووظائف تسجيل الدخول والخروج.
 * 
 * @param {object} props - خصائص المكون.
 * @param {React.ReactNode} props.children - المكونات الفرعية التي ستتمكن من الوصول إلى سياق المصادقة.
 * @returns {JSX.Element} - مزود سياق المصادقة.
 */
export const AuthProvider = ({ children }) => {
  // حالة المستخدم: تحتوي على بيانات المستخدم المسجل دخوله، أو null إذا لم يكن هناك مستخدم.
  const [user, setUser] = useState(null);
  // حالة التحميل: تشير إلى ما إذا كان يتم التحقق من حالة المصادقة (مثل التحقق من التوكن).
  const [loading, setLoading] = useState(true);

  /**
   * دالة `validateToken`.
   * تتحقق من صلاحية توكن المصادقة عن طريق إرسال طلب إلى نقطة نهاية ملف تعريف المستخدم.ل
   * 
   * @param {string} token - توكن المصادقة المراد التحقق منه.
   * @returns {Promise<object>} - وعد (Promise) يعود بكائن { valid: boolean, userData?: object, error?: string }.
   */
  const validateToken = async (token) => {
    try {
      // إرسال طلب GET مصادق عليه لجلب بيانات ملف تعريف المستخدم
      const userData = await authenticatedGet('/api/users/profile', token, {}, {
        component: 'AuthContext',
        operation: 'validateToken'
      });
      return { valid: true, userData }; // التوكن صالح، وإرجاع بيانات المستخدم
    } catch (error) {
      // إذا كان التوكن غير صالح أو حدث خطأ، إرجاع حالة غير صالحة
      return { valid: false, error: error.message };
    }
  };

  // تأثير جانبي (useEffect) يتم تشغيله مرة واحدة عند تحميل المكون
  // للتحقق من وجود توكن مصادقة حالي في التخزين المحلي.
  useEffect(() => {
    const checkExistingAuth = async () => {
      const token = localStorage.getItem('authToken'); // محاولة جلب التوكن من التخزين المحلي
      if (token) {
        // إذا وجد توكن، يتم التحقق من صلاحيته
        const validation = await validateToken(token);
        if (validation.valid) {
          // إذا كان التوكن صالحًا، يتم تعيين بيانات المستخدم وتحديث سياق Sentry
          const userData = { ...validation.userData, token };
          setUser(userData);
          setUserContext(userData); // تعيين سياق المستخدم في Sentry
        } else {
          // إذا كان التوكن غير صالح، يتم إزالته ومسح بيانات المستخدم وسياق Sentry
          localStorage.removeItem('authToken');
          setUser(null);
          clearUserContext(); // مسح سياق المستخدم من Sentry
        }
      }
      setLoading(false); // إنهاء حالة التحميل بعد التحقق
    };
    checkExistingAuth(); // استدعاء الدالة عند تحميل المكون
  }, []); // مصفوفة تبعيات فارغة تعني أن هذا التأثير يعمل مرة واحدة فقط

  /**
   * دالة `login`.
   * تقوم بتسجيل دخول المستخدم عن طريق حفظ توكن المصادقة وتعيين بيانات المستخدم.
   * 
   * @param {object} userData - كائن يحتوي على بيانات المستخدم وتوكن المصادقة (`token`).
   * @returns {Promise<object>} - وعد يعود بكائن { success: boolean, error?: string }.
   */
  const login = async (userData) => {
    try {
      if (!userData.token) {
        throw new Error('لم يتم توفير توكن مصادقة');
      }
      localStorage.setItem('authToken', userData.token); // حفظ التوكن في التخزين المحلي
      setUser(userData); // تعيين بيانات المستخدم
      setUserContext(userData); // تعيين سياق المستخدم في Sentry
      // تأخير بسيط لضمان تحديث واجهة المستخدم أو لغرض معين (100ms)
      await new Promise(resolve => setTimeout(resolve, 100));
      return { success: true }; // تسجيل الدخول بنجاح
    } catch (error) {
      // التقاط الخطأ وإرساله إلى Sentry
      captureErrorWithContext(error, {
        context: {
          component: 'AuthContext',
          operation: 'login',
        },
      });
      // مسح التوكن وبيانات المستخدم في حالة الفشل
      localStorage.removeItem('authToken');
      setUser(null);
      clearUserContext();
      return { success: false, error: error.message }; // تسجيل الدخول فشل
    }
  };

  /**
   * دالة `logout`.
   * تقوم بتسجيل خروج المستخدم عن طريق مسح بيانات المستخدم والتوكن من التخزين المحلي.
   */
  const logout = () => {
    setUser(null); // مسح بيانات المستخدم
    localStorage.removeItem('authToken'); // إزالة التوكن من التخزين المحلي
    clearUserContext(); // مسح سياق المستخدم من Sentry
  };

  // الكائن الذي سيتم توفيره عبر سياق المصادقة للمكونات الفرعية
  const value = {
    user, // بيانات المستخدم الحالي
    loading, // حالة التحميل
    login, // دالة تسجيل الدخول
    logout, // دالة تسجيل الخروج
    validateToken, // دالة التحقق من التوكن
    isAuthenticated: !!user, // هل المستخدم مصادق عليه؟ (قيمة منطقية)
    isAdmin: user?.role === 'admin' || user?.role === 'sudo', // هل المستخدم مدير؟
    isSudo: user?.role === 'sudo' // هل المستخدم لديه صلاحيات sudo؟
  };

  return (
    // توفير الكائن `value` لجميع المكونات الفرعية
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
