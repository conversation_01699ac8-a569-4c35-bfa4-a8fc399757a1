// frontend/src/utils/api.js
import { captureErrorWithContext } from '../config/sentry';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

/**
 * Enhanced fetch wrapper with Sentry error tracking
 * @param {string} endpoint - API endpoint (without base URL)
 * @param {Object} options - Fetch options
 * @param {Object} context - Additional context for error tracking
 * @returns {Promise} - Fetch response
 */
export const apiRequest = async (endpoint, options = {}, context = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const duration = Date.now() - startTime;

    // Log successful requests in development
    if (import.meta.env.MODE === 'development') {
      console.log(`API ${options.method || 'GET'} ${endpoint} - ${response.status} (${duration}ms)`);
    }

    // Handle non-2xx responses
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      error.status = response.status;
      error.response = response;
      error.data = errorData;
      
      // Capture API errors to Sentry (but not client errors like 401, 403, 404)
      if (response.status >= 500) {
        captureErrorWithContext(error, {
          context: {
            component: 'apiRequest',
            operation: context.operation || 'unknown',
            endpoint,
            method: options.method || 'GET',
            status: response.status,
            duration,
            ...context,
          },
        });
      }
      
      throw error;
    }

    return response;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // Capture network errors and other fetch failures
    if (error.name === 'TypeError' || error.name === 'NetworkError' || !error.status) {
      captureErrorWithContext(error, {
        context: {
          component: 'apiRequest',
          operation: context.operation || 'unknown',
          endpoint,
          method: options.method || 'GET',
          duration,
          errorType: 'network',
          ...context,
        },
      });
    }
    
    throw error;
  }
};

/**
 * GET request wrapper
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Additional options
 * @param {Object} context - Context for error tracking
 * @returns {Promise} - Response data
 */
export const apiGet = async (endpoint, options = {}, context = {}) => {
  const response = await apiRequest(endpoint, {
    method: 'GET',
    ...options,
  }, { operation: 'GET', ...context });
  
  return response.json();
};

/**
 * POST request wrapper
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {Object} options - Additional options
 * @param {Object} context - Context for error tracking
 * @returns {Promise} - Response data
 */
export const apiPost = async (endpoint, data = null, options = {}, context = {}) => {
  const response = await apiRequest(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : null,
    ...options,
  }, { operation: 'POST', ...context });
  
  return response.json();
};

/**
 * PUT request wrapper
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {Object} options - Additional options
 * @param {Object} context - Context for error tracking
 * @returns {Promise} - Response data
 */
export const apiPut = async (endpoint, data = null, options = {}, context = {}) => {
  const response = await apiRequest(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : null,
    ...options,
  }, { operation: 'PUT', ...context });
  
  return response.json();
};

/**
 * DELETE request wrapper
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Additional options
 * @param {Object} context - Context for error tracking
 * @returns {Promise} - Response data
 */
export const apiDelete = async (endpoint, options = {}, context = {}) => {
  const response = await apiRequest(endpoint, {
    method: 'DELETE',
    ...options,
  }, { operation: 'DELETE', ...context });
  
  return response.json();
};

/**
 * Create authenticated headers with token
 * @param {string} token - Authentication token
 * @returns {Object} - Headers object
 */
export const createAuthHeaders = (token) => ({
  'Authorization': `Bearer ${token}`,
});

/**
 * Authenticated API request wrapper
 * @param {string} endpoint - API endpoint
 * @param {string} token - Authentication token
 * @param {Object} options - Additional options
 * @param {Object} context - Context for error tracking
 * @returns {Promise} - Response
 */
export const authenticatedRequest = (endpoint, token, options = {}, context = {}) => {
  return apiRequest(endpoint, {
    headers: {
      ...createAuthHeaders(token),
      ...options.headers,
    },
    ...options,
  }, context);
};

/**
 * Authenticated GET request
 * @param {string} endpoint - API endpoint
 * @param {string} token - Authentication token
 * @param {Object} options - Additional options
 * @param {Object} context - Context for error tracking
 * @returns {Promise} - Response data
 */
export const authenticatedGet = async (endpoint, token, options = {}, context = {}) => {
  const response = await authenticatedRequest(endpoint, token, {
    method: 'GET',
    ...options,
  }, { operation: 'authenticated_GET', ...context });
  
  return response.json();
};

/**
 * Authenticated POST request
 * @param {string} endpoint - API endpoint
 * @param {string} token - Authentication token
 * @param {Object} data - Request body data
 * @param {Object} options - Additional options
 * @param {Object} context - Context for error tracking
 * @returns {Promise} - Response data
 */
export const authenticatedPost = async (endpoint, token, data = null, options = {}, context = {}) => {
  const response = await authenticatedRequest(endpoint, token, {
    method: 'POST',
    body: data ? JSON.stringify(data) : null,
    ...options,
  }, { operation: 'authenticated_POST', ...context });
  
  return response.json();
};

/**
 * Authenticated PUT request
 * @param {string} endpoint - API endpoint
 * @param {string} token - Authentication token
 * @param {Object} data - Request body data
 * @param {Object} options - Additional options
 * @param {Object} context - Context for error tracking
 * @returns {Promise} - Response data
 */
export const authenticatedPut = async (endpoint, token, data = null, options = {}, context = {}) => {
  const response = await authenticatedRequest(endpoint, token, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : null,
    ...options,
  }, { operation: 'authenticated_PUT', ...context });
  
  return response.json();
};

export default {
  apiRequest,
  apiGet,
  apiPost,
  apiPut,
  apiDelete,
  authenticatedRequest,
  authenticatedGet,
  authenticatedPost,
  authenticatedPut,
  createAuthHeaders,
};