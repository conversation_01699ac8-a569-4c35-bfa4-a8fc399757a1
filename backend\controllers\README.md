# متحكمات الواجهة الخلفية (Backend Controllers)

يحتوي هذا المجلد على متحكمات Express.js التي تتعامل مع منطق الأعمال الخاص بالطلبات الواردة من الواجهة الأمامية. كل ملف هنا يمثل مجموعة من الدوال المسؤولة عن معالجة الطلبات المتعلقة بكيان معين (مثل المستخدمين، الأسئلة، جلسات الاختبار).

## هيكل المجلد

يتكون المجلد من ملفات JavaScript، حيث يختص كل ملف بمعالجة الطلبات لنوع معين من الموارد.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `questionController.js`
يحتوي على دوال التحكم (controller functions) لمعالجة عمليات CRUD (الإنشاء، القراءة، التحديث، الحذف) المتعلقة بالأسئلة. يتعامل مع أنواع مختلفة من الأسئلة (مثل أسئلة الخطأ، التشبيه، الإكمال، فهم المقروء، الشاذ) ويستخدم نماذج Mongoose المناسبة لكل نوع.

### `testController.js`
يحتوي على دوال التحكم لإدارة جلسات الاختبار. يتضمن وظائف لبدء اختبارات جديدة (جلب أسئلة عشوائية)، حفظ إجابات المستخدم، وجلب وتقديم جلسات الاختبار، وحساب النتائج النهائية، وتحديث إحصائيات المستخدم والسؤال.

### `userController.js`
يحتوي على دوال التحكم لإدارة المستخدمين. يتعامل مع مصادقة المستخدمين عبر Telegram (بما في ذلك التحقق الأمني من البيانات)، وإدارة ملفاتهم الشخصية (جلب وتحديث)، وجلب إحصائيات أدائهم في الاختبارات.
