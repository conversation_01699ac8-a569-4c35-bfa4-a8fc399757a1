/**
 * مكون UserStats (إحصائيات المستخدم).
 * يعرض إحصائيات أداء المستخدم مثل عدد الاختبارات المكتملة ومتوسط الدرجات.
 * 
 * @param {Object} props - خصائص المكون
 * @param {Object|null} props.userStats - إحصائيات المستخدم
 * @param {number} props.userStats.totalTests - عدد الاختبارات المكتملة
 * @param {number} props.userStats.averageScore - متوسط الدرجة
 * @param {number} props.userStats.bestScore - أفضل درجة
 * @returns {JSX.Element} - مكون إحصائيات المستخدم
 */
const UserStats = ({ userStats }) => {
  // إذا لم تكن هناك إحصائيات، عرض رسالة تحميل
  if (!userStats) {
    return (
      <section className="stats-section">
        <h2 className="section-title">أداؤك</h2>
        <div className="stats-loading">
          <p>جاري تحميل الإحصائيات...</p>
        </div>
      </section>
    );
  }

  // إذا كانت جميع الإحصائيات صفر، عرض رسالة "لا توجد بيانات"
  if (userStats.totalTests === 0 && userStats.averageScore === 0 && userStats.bestScore === 0) {
    return (
      <section className="stats-section">
        <h2 className="section-title">أداؤك</h2>
        <div className="stats-no-data">
          <p>لا توجد إحصائيات متاحة بعد. ابدأ اختبارك الأول لرؤية أداؤك!</p>
        </div>
      </section>
    );
  }

  return (
    <section className="stats-section">
      <h2 className="section-title">أداؤك</h2>
      <div className="stats-grid">
        {/* بطاقة إحصائية: الاختبارات المكتملة */}
        <div className="stat-card">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <div className="stat-value">{userStats.totalTests || 0}</div>
            <div className="stat-label">اختبارات مكتملة</div>
          </div>
        </div>
        {/* بطاقة إحصائية: متوسط الدرجة */}
        <div className="stat-card">
          <div className="stat-icon">🎯</div>
          <div className="stat-content">
            <div className="stat-value">{userStats.averageScore || 0}%</div>
            <div className="stat-label">متوسط الدرجة</div>
          </div>
        </div>
        {/* بطاقة إحصائية: أفضل درجة */}
        <div className="stat-card">
          <div className="stat-icon">⭐</div>
          <div className="stat-content">
            <div className="stat-value">{userStats.bestScore || 0}</div>
            <div className="stat-label">أفضل درجة</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default UserStats;
