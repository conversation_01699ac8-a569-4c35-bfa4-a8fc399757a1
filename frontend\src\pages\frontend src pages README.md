# صفحات الواجهة الأمامية (Frontend Pages)

يحتوي هذا المجلد على المكونات الرئيسية التي تمثل صفحات التطبيق المختلفة. كل ملف هنا يمثل عادةً مسارًا أو عرضًا رئيسيًا في التطبيق، ويجمع بين المكونات الأصغر لإنشاء تجربة مستخدم كاملة.

## هيكل المجلد

يتكون المجلد من ملفات `JSX` التي تمثل الصفحات، بالإضافة إلى ملفات `CSS` التي توفر التنسيقات الخاصة ببعض هذه الصفحات.

## قائمة الصفحات والملفات

فيما يلي وصف لكل ملف ومكون داخل هذا المجلد:

### `AdminQuestionsPage.jsx`
صفحة إدارة الأسئلة المخصصة للمديرين. تسمح بعرض، إضافة، تعديل، وحذف الأسئلة في قاعدة البيانات. تتضمن منطق التحقق من الصلاحيات، جلب البيانات، ومعالجة عمليات الحذف.

### `AuthCallbackPage.jsx`
صفحة رد نداء المصادقة. لا تعرض أي واجهة مستخدم، بل تتعامل مع استجابة المصادقة من مزود خارجي (مثل تليجرام). تقوم بمعالجة التوكن أو رسائل الخطأ من عنوان URL وتسجيل دخول المستخدم أو إعادة توجيهه.

### `DashboardPage.jsx`
صفحة لوحة التحكم الرئيسية للمستخدم. تعرض رسالة ترحيب شخصية، خيارات سريعة لبدء اختبار جديد، وإحصائيات أداء المستخدم. تتفاعل مع الواجهة الخلفية لجلب الإحصائيات وبدء جلسات الاختبار.

### `HomePage.jsx`
الصفحة الرئيسية للتطبيق للمستخدمين غير المسجلين. تعرض معلومات حول المنصة، وميزاتها الرئيسية، وتشجع المستخدمين على تسجيل الدخول. تقوم بإعادة توجيه المستخدمين المسجلين تلقائيًا إلى لوحة التحكم.

### `ResultsPage.jsx`
صفحة عرض نتائج الاختبار. تعرض ملخصًا لنتائج الاختبار المكتمل، بما في ذلك الدرجة الإجمالية وتوزيع الإجابات الصحيحة والخاطئة وغير المجابة، باستخدام رسم بياني دائري. توفر خيارات للعودة إلى لوحة التحكم أو إجراء اختبار آخر.

### `TelegramCallbackProxy.jsx`
مكون وكيل لرد نداء تليجرام. يعمل كوسيط بين رد نداء مصادقة تليجرام والواجهة الخلفية. يقوم بالتقاط معلمات الاستجابة من عنوان URL وإعادة توجيهها إلى الواجهة الخلفية لإكمال عملية المصادقة.

### `TestingPage.css`
ملف التنسيقات (CSS) الخاص بصفحة الاختبار. يوفر الأنماط البصرية لواجهة الاختبار بأكملها، بما في ذلك تخطيط الصفحة، شريط التقدم، تنسيقات الأسئلة والخيارات، التغذية الراجعة، وشاشة النتائج النهائية.

### `TestingPage.jsx`
المكون الرئيسي لصفحة الاختبار. يدير تجربة الاختبار بأكملها، بما في ذلك عرض الأسئلة، معالجة إجابات المستخدم، التنقل بين الأسئلة (بما في ذلك الأسئلة الفرعية لفهم المقروء)، وتوفير التغذية الراجعة الفورية، وتقديم الاختبار. يتضمن منطقًا معقدًا لإدارة الحالة والتفاعل مع API.
