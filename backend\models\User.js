// backend/models/User.js

// استيراد مكتبة Mongoose للتعامل مع MongoDB
const mongoose = require('mongoose');

/**
 * `userSchema` (نموذج المستخدم).
 * يحدد بنية المستند لمستخدم في التطبيق.
 * تم تصميمه ليتناسب مع المصادقة عبر Telegram، مع إزالة الحقول المتعلقة بكلمة المرور.
 * 
 * @property {number} telegramId - المعرف الرقمي الفريد للمستخدم من Telegram.
 *   - `required: true` - إلزامي.
 *   - `unique: true` - يجب أن يكون فريدًا.
 *   - `index: true` - إنشاء فهرس على هذا الحقل لزيادة سرعة البحث.
 * 
 * @property {string} fullName - الاسم الكامل للمستخدم، يتم الحصول عليه من Telegram.
 *   - `required: true` - إلزامي.
 *   - `trim: true` - إزالة المسافات البيضاء من البداية والنهاية.
 * 
 * @property {string} username - اسم المستخدم في Telegram (يبدأ بـ @)، اختياري.
 *   - `trim: true` - إزالة المسافات البيضاء.
 * 
 * @property {string} email - البريد الإلكتروني للمستخدم، اختياري.
 *   - `trim: true` - إزالة المسافات البيضاء.
 *   - `lowercase: true` - تحويل البريد الإلكتروني إلى أحرف صغيرة.
 *   - `unique: true` - يجب أن يكون فريدًا (للقيم غير الفارغة).
 *   - `sparse: true` - يسمح بوجود عدة مستندات بدون بريد إلكتروني (null)، ولكنه يضمن أن كل بريد إلكتروني مُدخل هو فريد.
 * 
 * @property {string} role - دور المستخدم في التطبيق.
 *   - `enum: ['sudo', 'admin', 'user', 'visitor', 'banned']` - الأدوار المسموح بها.
 *   - `default: 'visitor'` - الدور الافتراضي للمستخدمين الجدد.
 * 
 * @property {number} level - مستوى أداء المستخدم (عادةً درجة الاختبار).
 *   - `required: true` - إلزامي.
 *   - `default: 0` - القيمة الافتراضية هي 0.
 *   - `min: 0`, `max: 100` - نطاق المستوى.
 * 
 * @property {object} preferences - تفضيلات المستخدم.
 *   - `language: { type: String, default: 'ar' }` - لغة الواجهة المفضلة، الافتراضي هو العربية.
 * 
 * @options
 *   `timestamps: true` - يضيف حقلي `createdAt` و `updatedAt` تلقائيًا إلى المستند.
 */
const userSchema = new mongoose.Schema({
    telegramId: {
        type: Number,
        required: true,
        unique: true,
        index: true // نضيف فهرسًا (index) على هذا الحقل لزيادة سرعة البحث
    },
    fullName: {
        type: String,
        required: true,
        trim: true
    },
    username: {
        type: String,
        trim: true
    },
    email: {
        type: String,
        trim: true,
        lowercase: true,
        unique: true,
        sparse: true, // يسمح بوجود عدة مستندات بدون بريد إلكتروني (null)، ولكنه يضمن أن كل بريد إلكتروني مُدخل هو فريد.
    },
    role: {
        type: String,
        enum: ['sudo', 'admin', 'user', 'visitor', 'banned'],
        default: 'visitor'
    },
    level: {
        type: Number,
        required: true,
        default: 0,
        min: 0,
        max: 100
    },
    preferences: {
        language: { type: String, default: 'ar' },
    },
}, {
    timestamps: true // لإضافة createdAt و updatedAt تلقائيًا
});

// إنشاء نموذج User من المخطط
const User = mongoose.model('User', userSchema);

// تصدير النموذج لاستخدامه في أجزاء أخرى من التطبيق
module.exports = User;
