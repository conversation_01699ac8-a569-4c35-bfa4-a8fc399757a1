/* --- تنسيقات حاوية شريط التنقل الرئيسية --- */
.navbar-container {
  position: fixed; /* تثبيت الشريط في أعلى الصفحة */
  top: 0;
  left: 0;
  width: 100%; /* عرض كامل */
  height: 60px; /* ارتفاع محدد */
  padding: 0 2rem; /* هوامش داخلية */
  background-color: white; /* لون الخلفية */
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 5px 0 rgba(0, 0, 0, 0.04); /* إضافة ظل خفيف */
  display: flex; /* استخدام Flexbox للتخطيط */
  align-items: center; /* محاذاة العناصر عموديًا */
  justify-content: space-between; /* توزيع المسافة بين العناصر */
  z-index: 1000; /* ضمان بقاء الشريط فوق العناصر الأخرى */
}

/* تنسيق الأجزاء اليمنى واليسرى من الشريط */
.navbar-left,
.navbar-right {
  display: flex;
  align-items: center;
  height: 100%;
}

/* فاصل بين عناصر الشريط */
.navbar-item-spacer {
  width: 1rem;
}

/* تنسيق شعار الموقع أو اسمه */
.navbar-brand {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
  text-decoration: none; /* إزالة الخط تحت الرابط */
  display: flex;
  align-items: center;
  height: 100%;
}

/* --- تنسيقات زر تسجيل الخروج (.Btn) --- */
.Btn {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* محاذاة المحتوى إلى اليسار */
  width: 45px; /* العرض الأولي */
  height: 45px; /* الارتفاع */
  border: none; /* بدون حدود */
  border-radius: 50%; /* شكل دائري */
  cursor: pointer;
  position: relative; /* ضروري لتحديد موضع النص الداخلي */
  overflow: hidden; /* إخفاء النص الزائد عن حدود الزر */
  transition-duration: .3s; /* مدة حركة الانتقال */
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.199); /* ظل للزر */
  background-color: rgb(255, 65, 65); /* لون الخلفية أحمر */
}

/* تنسيق حاوية أيقونة تسجيل الخروج */
.Btn .sign {
  width: 100%;
  transition-duration: .3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* تنسيق أيقونة SVG نفسها */
.Btn .sign svg {
  width: 17px;
}
.Btn .sign svg path {
  fill: white; /* لون الأيقونة أبيض */
}

/* تنسيق نص "خروج" الذي يظهر عند المرور */
.Btn .text {
  position: absolute; /* تحديد الموضع بالنسبة للزر الرئيسي */
  right: 0%;
  width: 0%; /* العرض الأولي صفر (مخفي) */
  opacity: 0; /* الشفافية صفر (مخفي) */
  color: white;
  font-size: 1.2em;
  font-weight: 600;
  transition-duration: .3s; /* مدة حركة الظهور */
}

/* التنسيقات عند مرور مؤشر الفأرة فوق الزر */
.Btn:hover {
  width: 125px; /* توسيع عرض الزر */
  border-radius: 40px; /* تغيير شكل الحواف */
  transition-duration: .3s;
}

/* تحريك الأيقونة إلى اليسار عند مرور المؤشر */
.Btn:hover .sign {
  width: 30%;
  transition-duration: .3s;
  padding-left: 20px;
}

/* إظهار النص عند مرور المؤشر */
.Btn:hover .text {
  opacity: 1;
  width: 70%;
  transition-duration: .3s;
  padding-right: 10px;
}

/* تأثير بسيط عند النقر على الزر */
.Btn:active {
  transform: translate(2px ,2px);
}

/* --- تنسيقات مفتاح تبديل المظهر (.ui-switch) --- */
.ui-switch {
  /* متغيرات CSS للتحكم في أبعاد وألوان المفتاح */
  --switch-bg: rgb(135, 150, 165); /* لون خلفية المفتاح */
  --switch-width: 48px; /* عرض المفتاح */
  --switch-height: 20px; /* ارتفاع المفتاح */
  --circle-diameter: 32px; /* قطر الدائرة المتحركة */
  --circle-bg: rgb(0, 56, 146); /* لون خلفية الدائرة */
  --circle-inset: calc((var(--circle-diameter) - var(--switch-height)) / 2); /* حساب المسافة لمركزة الدائرة */
}

/* إخفاء مربع الاختيار الافتراضي */
.ui-switch input { display: none; }

/* تنسيق الشريط المنزلق للمفتاح */
.ui-switch .slider {
  width: var(--switch-width);
  height: var(--switch-height);
  background: var(--switch-bg);
  border-radius: 999px; /* حواف دائرية بالكامل */
  position: relative;
  cursor: pointer;
}

/* تنسيق الدائرة المتحركة */
.ui-switch .slider .circle {
  top: calc(var(--circle-inset) * -1); /* تحديد الموضع العمودي */
  left: 0; /* الموضع الأفقي الأولي */
  width: var(--circle-diameter);
  height: var(--circle-diameter);
  position: absolute;
  background: var(--circle-bg);
  border-radius: inherit; /* وراثة نفس شكل الحواف */
  /* أيقونة الشمس (الوضع الفاتح) كخلفية للدائرة */
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjAiIHdpZHRoPSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj4KICAgIDxwYXRoIGZpbGw9IiNmZmYiCiAgICAgICAgZD0iTTkuMzA1IDEuNjY3VjMuNzVoMS4zODlWMS42NjdoLTEuMzl6bS00LjcwNyAxLjk1bC0uOTgyLjk4Mkw1LjA5IDYuMDcybC45ODItLjk4Mi0xLjQ3My0xLjQ3M3ptMTAuODAyIDBMMTMuOTI3IDUuMDlsLjk4Mi45ODIgMS40NzMtMS40NzMtLjk4Mi0uOTgyek0xMCA1LjEzOWE0Ljg3MiA0Ljg3MiAwIDAwLTQuODYyIDQuODZBNC44NzIgNC44NzIgMCAwMDEwIDE0Ljg2MiA0Ljg3MiA0Ljg3MiAwIDAwMTQuODYgMTAgNC44NzIgNC44NzIgMCAwMDEwIDUuMTM5em0wIDEuMzg5QTMuNDYyIDMuNDYyIDAgMDExMy40NzEgMTBhMy40NjIgMy40NjIgMCAwMS0zLjQ3MyAzLjQ3MkEzLjQ2MiAzLjQ2MiAwIDAxNi41MjcgMTAgMy40NjIgMy40NjIgMCAwMTEwIDYuNTI4ek0xLjY2NSA5LjMwNXYxLjM5aDIuMDgzdi0xLjM5SDEuNjY2em0xNC41ODMgMHYxLjM5aDIuMDg0di0xLjM5aC0yLjA4NHpNNS4wOSAxMy45MjhMMy42MTYgMTUuNGwuOTgyLjk4MiAxLjQ3My0xLjQ3My0uOTgyLS45ODJ6bTkuODIgMGwtLjk4Mi45ODIgMS40NzMgMS40NzMuOTgyLS45ODItMS40NzMtMS40NzN6TTkuMzA1IDE2LjI1djIuMDgzaDEuMzg5VjE2LjI1aC0xLjM5eiIgLz4KPC9zdmc+");
  background-repeat: no-repeat;
  background-position: center center;
  transition: left 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; /* حركة انتقال سلسة */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 2px 1px -1px rgba(0,0,0,0.2), 0px 1px 1px 0px rgba(0,0,0,0.14), 0px 1px 3px 0px rgba(0,0,0,0.12);
}

/* عند تفعيل المفتاح (وضع علامة checked) */
.ui-switch input:checked+.slider .circle {
  left: calc(100% - var(--circle-diameter)); /* تحريك الدائرة إلى اليمين */
  /* تغيير أيقونة الخلفية إلى القمر (الوضع الداكن) */
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjAiIHdpZHRoPSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj4KICAgIDxwYXRoIGZpbGw9IiNmZmYiCiAgICAgICAgZD0iTTQuMiAyLjVsLS43IDEuOC0xLjguNyAxLjguNy43IDEuOC42LTEuOEw2LjcgNWwtMS45LS43LS42LTEuOHptMTUgOC4zYTYuNyA2LjcgMCAxMS02LjYtNi42IDUuOCA1LjggMCAwMDYuNiA2LjZ6IiAvPgo8L3N2Zz4=");
}
