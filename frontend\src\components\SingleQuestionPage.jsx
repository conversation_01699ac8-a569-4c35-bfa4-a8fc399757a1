// هذا الملف لا يستورد React بشكل صريح لأنه يستخدم JSX الذي يتم تحويله بواسطة Babel
// ولكن من الممارسات الجيدة استيراده إذا كنت تستخدم هوكس أو ميزات React أخرى.

/**
 * مكون SingleQuestionPage لعرض سؤال واحد مع خيارات الإجابة.
 * يسمح للمستخدم باختيار إجابة واحدة ويعرض التغذية الراجعة (صحيح/خطأ) بعد الإجابة.
 * 
 * @param {object} props - الخصائص المستلمة من المكون الأب.
 * @param {object} props.question - كائن السؤال الذي يحتوي على:
 *   - `question_text`: نص السؤال.
 *   - `options`: كائن يحتوي على خيارات الإجابة (مثال: "A": { text: "الخيار أ" }, "B": { text: "الخيار ب" } }).
 *   - `correct_option`: مفتاح الخيار الصحيح (مثال: "A").
 * @param {string|null} props.userAnswer - مفتاح الخيار الذي اختاره المستخدم، أو `null` إذا لم يتم الإجابة بعد.
 * @param {function} props.onAnswer - دالة يتم استدعاؤها عند اختيار المستخدم لإجابة، وتستقبل مفتاح الخيار المختار.
 * @returns {JSX.Element} - عرض السؤال مع خياراته.
 */
const SingleQuestionPage = ({ question, userAnswer, onAnswer }) => {
    // تحديد ما إذا كان المستخدم قد أجاب على السؤال بالفعل
    const hasAnswered = userAnswer !== null;

    /**
     * معالج النقر على خيار الإجابة.
     * يسمح للمستخدم بالنقر على الخيار مرة واحدة فقط.
     * @param {string} optionKey - مفتاح الخيار الذي تم النقر عليه (مثال: "A", "B").
     */
    const handleOptionClick = (optionKey) => {
        // إذا لم يتم الإجابة على السؤال بعد، قم باستدعاء دالة onAnswer
        if (!hasAnswered) {
            onAnswer(optionKey);
        }
    };

    return (
        // بطاقة السؤال الرئيسية. تستخدم تنسيقات مضمنة بالإضافة إلى تنسيقات CSS الخارجية.
        <div className="question-card" style={{ border: '1px solid #ddd', padding: '1em', margin: '1em 0', textAlign: 'right', borderRadius: '8px' }}>
            {/* عرض نص السؤال */}
            <h2>{question?.question_text}</h2>
            
            {/* شبكة لعرض خيارات الإجابة */}
            <div className="options-grid" style={{ marginTop: '1em' }}>
                {/* التحقق من وجود خيارات السؤال ثم تكرارها */}
                {question?.options && Object.entries(question.options).map(([key, option]) => {
                    // تعريف التنسيقات الأساسية لكل خيار
                    let optionStyle = { 
                        padding: '10px', 
                        margin: '5px 0', 
                        borderWidth: '2px',
                        borderStyle: 'solid',
                        borderColor: '#ddd',
                        borderRadius: '8px', 
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                    };
                    
                    // تطبيق تنسيقات إضافية إذا كان المستخدم قد أجاب على السؤال
                    if (hasAnswered) {
                        optionStyle.cursor = 'default'; // تعطيل النقر بعد الإجابة
                        // إذا كان الخيار الحالي هو الإجابة الصحيحة
                        if (key === question.correct_option) {
                            optionStyle.backgroundColor = '#d4edda'; // خلفية خضراء فاتحة
                            optionStyle.borderColor = '#28a745'; // حدود خضراء
                        } 
                        // إذا كان الخيار الحالي هو إجابة المستخدم (وكانت خاطئة)
                        else if (key === userAnswer) {
                            optionStyle.backgroundColor = '#f8d7da'; // خلفية حمراء فاتحة
                            optionStyle.borderColor = '#dc3545'; // حدود حمراء
                        }
                    }

                    return (
                        // عرض كل خيار مع تطبيق التنسيقات ومعالج النقر
                        <div key={key} style={optionStyle} onClick={() => handleOptionClick(key)}>
                            <strong>{key}:</strong> {option?.text}
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

// تصدير المكون ليكون متاحًا للاستخدام في أجزاء أخرى من التطبيق
export default SingleQuestionPage;