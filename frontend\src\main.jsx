import React from 'react';
import ReactDOM from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { TestProvider } from './context/TestContext';

import App from './App.jsx';
import HomePage from './pages/HomePage';
import DashboardPage from './pages/DashboardPage';
import TestingPage from './pages/TestingPage.jsx';
import ResultsPage from './pages/ResultsPage.jsx';
import AuthCallbackPage from './pages/AuthCallbackPage'; // <-- 1. استيراد صفحة الـ Callback

import './index.css';

const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      { 
        index: true, 
        element: <HomePage /> 
      },
      { 
        path: 'dashboard', 
        element: <DashboardPage /> 
      },
      { 
        path: 'testing/:sessionId', 
        element: <TestingPage /> 
      },
      { 
        path: 'results', 
        element: <ResultsPage /> 
      },
      {
        // --- 2. إضافة المسار المفقود هنا ---
        path: 'auth/callback',
        element: <AuthCallbackPage />,
      },
    ],
  },
]);

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <AuthProvider>
      <TestProvider>
        <RouterProvider router={router} />
      </TestProvider>
    </AuthProvider>
  </React.StrictMode>
);