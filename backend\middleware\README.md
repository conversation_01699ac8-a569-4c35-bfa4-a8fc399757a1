# وسيطات الواجهة الخلفية (Backend Middleware)

يحتوي هذا المجلد على دوال وسيطة (middleware functions) تستخدم في تطبيق Express.js. تعمل هذه الدوال على معالجة الطلبات والاستجابات في مراحل مختلفة من دورة حياة الطلب، مما يوفر وظائف مشتركة مثل المصادقة، معالجة الأخطاء، والتحقق من صحة البيانات.

## هيكل المجلد

يتكون المجلد من ملفات JavaScript، حيث يختص كل ملف بنوع معين من المعالجة الوسيطة.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `authMiddleware.js`
يوفر دوال وسيطة للمصادقة والترخيص. يتضمن دالة `protect` للتحقق من توكنات JWT وتحديد هوية المستخدم، ودالة `authorizeRoles` للتحقق من أدوار المستخدمين، ودالة `authorizeSudo` للتحقق من صلاحيات المستخدمين ذوي دور 'sudo'.

### `errorMiddleware.js`
يحتوي على دوال وسيطة مركزية لمعالجة الأخطاء. يتضمن دالة `notFound` للتعامل مع المسارات غير الموجودة (404)، ودالة `errorHandler` الشاملة التي تلتقط الأخطاء، وتضبط رموز حالة HTTP، وتتعامل مع أخطاء Mongoose، وتسجل الأخطاء باستخدام Winston، وترسلها إلى Sentry بشكل مشروط.

### `validationMiddleware.js`
يحتوي على دالة وسيطة لمعالجة أخطاء التحقق من صحة البيانات. تستخدم `express-validator` لجمع أخطاء التحقق، وإذا وجدت، ترسل استجابة 400 (طلب سيء) مع تفاصيل الأخطاء إلى العميل.
