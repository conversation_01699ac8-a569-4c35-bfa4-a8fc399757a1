# سياقات الواجهة الأمامية (Frontend Contexts)

يحتوي هذا المجلد على سياقات React (Context API) التي توفر طريقة لمشاركة البيانات والوظائف عبر شجرة المكونات دون الحاجة إلى تمرير الخصائص يدويًا في كل مستوى. هذه السياقات ضرورية لإدارة الحالة العامة للتطبيق مثل المصادقة، بيانات الاختبار، والمظهر.

## هيكل المجلد

يتكون المجلد من ملفات `JSX`، حيث يمثل كل ملف سياقًا محددًا ومزودًا (Provider) خاصًا به، بالإضافة إلى هوك مخصص (`use...`) لتسهيل استهلاك السياق.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `AuthContext.jsx`
يوفر هذا الملف سياق المصادقة للتطبيق. يدير حالة تسجيل دخول المستخدم، ويتحقق من صلاحية توكن المصادقة، ويوفر دوال لتسجيل الدخول والخروج. كما يتكامل مع Sentry لتتبع المستخدمين والأخطاء المتعلقة بالمصادقة.

### `TestContext.jsx`
يوفر هذا الملف سياقًا لإدارة حالة جلسة الاختبار أو الاختبارات القصيرة. يدير قائمة أسئلة الاختبار ونتائج الاختبار، ويوفر دالة لبدء اختبار جديد.

### `ThemeContext.jsx`
يوفر هذا الملف سياقًا لإدارة مظهر التطبيق (الوضع الفاتح والداكن). يقوم بحفظ تفضيل المظهر في التخزين المحلي للمتصفح، ويطبق الفئات المناسبة على جسم المستند، ويوفر دالة لتبديل المظهر.
