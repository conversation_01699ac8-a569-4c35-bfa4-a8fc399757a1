// استيراد المكونات اللازمة من مكتبة react-router-dom
import { Navigate, useLocation } from 'react-router-dom';
// استيراد हुक useAuth للوصول إلى بيانات المصادقة
import { useAuth } from '../context/AuthContext';

/**
 * مكون ProtectedRoute لحماية المسارات التي تتطلب تسجيل دخول المستخدم.
 * يتحقق هذا المكون مما إذا كان المستخدم قد قام بتسجيل الدخول.
 * 
 * @param {object} props - الخصائص المستلمة من المكون.
 * @param {React.ReactNode} props.children - المكونات الفرعية التي سيتم عرضها إذا كان المستخدم مسجلاً للدخول.
 * @returns {JSX.Element} - يعرض المكونات الفرعية المحمية، أو يعيد التوجيه إلى صفحة تسجيل الدخول.
 */
const ProtectedRoute = ({ children }) => {
    // الحصول على حالة المستخدم وحالة التحميل من سياق المصادقة
    const { user, loading } = useAuth();
    // الحصول على معلومات الموقع الحالي للتوجيه بعد تسجيل الدخول
    const location = useLocation();

    // عرض مؤشر تحميل أثناء التحقق من حالة المصادقة
    if (loading) {
        return (
            <div className="loading">
                <div className="spinner"></div>
                <span>يتم التحقق من المصادقة...</span>
            </div>
        );
    }

    // إذا لم يكن المستخدم مسجلاً للدخول، يتم إعادة توجيهه إلى الصفحة الرئيسية
    if (!user) {
        // Navigate يقوم بإعادة التوجيه مع حفظ الموقع الأصلي للعودة إليه بعد تسجيل الدخول
        return <Navigate to="/" state={{ from: location }} replace />;
    }

    // إذا كان المستخدم مسجلاً للدخول، يتم عرض المحتوى المحمي (المكونات الفرعية)
    return children;
};

// تصدير المكون لاستخدامه في أجزاء أخرى من التطبيق
export default ProtectedRoute;
