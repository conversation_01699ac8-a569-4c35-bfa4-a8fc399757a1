const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');

let mongoServer; // متغير لتخزين خادم MongoDB في الذاكرة

/**
 * دالة `beforeAll`.
 * يتم تشغيلها مرة واحدة فقط قبل بدء جميع الاختبارات في مجموعة الاختبارات.
 * تقوم بإنشاء خادم MongoDB مؤقت في الذاكرة والاتصال به.
 */
beforeAll(async () => {
  // إنشاء خادم MongoDB في الذاكرة (يتم تنزيله تلقائيًا إذا لم يكن موجودًا)
  mongoServer = await MongoMemoryServer.create();
  // الحصول على URI الاتصال بقاعدة البيانات المؤقتة
  const mongoUri = mongoServer.getUri();
  
  // الاتصال بقاعدة البيانات المؤقتة باستخدام Mongoose
  await mongoose.connect(mongoUri);
});

/**
 * دالة `afterAll`.
 * يتم تشغيلها مرة واحدة فقط بعد انتهاء جميع الاختبارات في مجموعة الاختبارات.
 * تقوم بقطع الاتصال بقاعدة البيانات وإيقاف خادم MongoDB المؤقت.
 */
afterAll(async () => {
  // قطع الاتصال بقاعدة البيانات
  await mongoose.disconnect();
  // إيقاف خادم MongoDB في الذاكرة وتحرير الموارد
  await mongoServer.stop();
});

/**
 * دالة `beforeEach`.
 * يتم تشغيلها قبل بدء كل اختبار على حدة.
 * تقوم بمسح جميع البيانات من جميع المجموعات (collections) في قاعدة البيانات
 * لضمان أن كل اختبار يبدأ ببيانات نظيفة ومستقلة عن الاختبارات الأخرى.
 */
beforeEach(async () => {
  // الحصول على جميع المجموعات (collections) في قاعدة البيانات المتصلة
  const collections = mongoose.connection.collections;
  // المرور على كل مجموعة وحذف جميع المستندات منها
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({}); // حذف جميع المستندات
  }
});
