// استيراد هوكس React لإدارة الحالة، دورة الحياة، وتحسين الأداء
import { useState, useEffect, useCallback, memo } from 'react';
// استيراد هوكس React Router للوصول إلى معلمات المسار، التنقل، وحالة الموقع
import { useParams, useNavigate, useLocation } from 'react-router-dom';
// استيراد هوك useAuth للوصول إلى بيانات المستخدم
import { useAuth } from '../context/AuthContext';
// استيراد مكون زر الإشارة المرجعية
import BookmarkButton from '../components/BookmarkButton';
// استيراد دالة Sentry لالتقاط الأخطاء مع السياق
import { captureErrorWithContext } from '../config/sentry';
// استيراد دوال API لإجراء طلبات مصادق عليها (GET, PUT, POST)
import { authenticatedGet, authenticatedPut, authenticatedPost } from '../utils/api';

/**
 * مكون TestingPage (صفحة الاختبار).
 * يتعامل هذا المكون مع واجهة الاختبار الرئيسية، ويوفر تجربة اختبار غامرة بملء الشاشة
 * مع التنقل بين الأسئلة، ومعالجة إجابات المستخدم، وتقديم الاختبار.
 * 
 * @returns {JSX.Element} - صفحة الاختبار.
 */
const TestingPage = () => {
    // استخراج معرف الجلسة من معلمات URL
    const { sessionId } = useParams();
    // هوك للتنقل البرمجي
    const navigate = useNavigate();
    // هوك للوصول إلى كائن الموقع (location object)
    const location = useLocation();
    // الحصول على بيانات المستخدم من سياق المصادقة
    const { user } = useAuth();

    // الحصول على إعداد التغذية الراجعة الفورية من حالة التنقل، الافتراضي هو false
    const instantFeedbackEnabled = location.state?.instantFeedbackEnabled || false;

    // حالات المكون لإدارة جوانب مختلفة من الاختبار
    const [testSession, setTestSession] = useState(null); // بيانات جلسة الاختبار
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0); // فهرس السؤال الرئيسي الحالي
    const [currentSubQuestionIndex, setCurrentSubQuestionIndex] = useState(0); // فهرس السؤال الفرعي الحالي (لفهم المقروء)
    const [userAnswers, setUserAnswers] = useState({}); // إجابات المستخدم المخزنة محليًا
    const [loading, setLoading] = useState(true); // حالة التحميل الأولية
    const [error, setError] = useState(''); // رسالة الخطأ
    const [isSubmitting, setIsSubmitting] = useState(false); // حالة تقديم الاختبار
    const [hoveredOption, setHoveredOption] = useState(null); // الخيار الذي يتم التحويم عليه (لبعض أنواع الأسئلة)
    const [expandedPassage, setExpandedPassage] = useState(false); // حالة توسيع/طي مقطع القراءة
    const [showFeedback, setShowFeedback] = useState(false); // إظهار التغذية الراجعة الفورية
    const [feedbackType, setFeedbackType] = useState(null); // نوع التغذية الراجعة ('correct' أو 'wrong')
    const [autoAdvanceTimer, setAutoAdvanceTimer] = useState(null); // مؤقت للتقدم التلقائي
    const [isFullScreen, setIsFullScreen] = useState(false); // حالة ملء الشاشة
    const [focusMode, setFocusMode] = useState(false); // وضع التركيز (يخفي عناصر الواجهة غير الضرورية)

    /**
     * دالة `toggleFullScreen`.
     * تبديل وضع ملء الشاشة للتطبيق.
     * تم استخدام `useCallback` لتحسين الأداء.
     */
    const toggleFullScreen = useCallback(() => {
        if (!document.fullscreenElement) {
            // الدخول في وضع ملء الشاشة
            document.documentElement.requestFullscreen().then(() => {
                setIsFullScreen(true);
                setFocusMode(true); // تفعيل وضع التركيز عند الدخول في ملء الشاشة
            }).catch(err => {
                console.warn('فشل الدخول في وضع ملء الشاشة:', err);
            });
        } else {
            // الخروج من وضع ملء الشاشة
            document.exitFullscreen().then(() => {
                setIsFullScreen(false);
                setFocusMode(false); // تعطيل وضع التركيز عند الخروج من ملء الشاشة
            }).catch(err => {
                console.warn('فشل الخروج من وضع ملء الشاشة:', err);
            });
        }
    }, []);

    // تأثير جانبي (useEffect) للاستماع إلى تغييرات وضع ملء الشاشة
    useEffect(() => {
        const handleFullscreenChange = () => {
            const isCurrentlyFullscreen = !!document.fullscreenElement;
            setIsFullScreen(isCurrentlyFullscreen);
            if (!isCurrentlyFullscreen) {
                setFocusMode(false); // تعطيل وضع التركيز إذا تم الخروج من ملء الشاشة يدويًا
            }
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        // دالة التنظيف لإزالة المستمع عند إلغاء تحميل المكون
        return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
    }, []); // مصفوفة تبعيات فارغة تعني أن هذا التأثير يعمل مرة واحدة فقط

    // تأثير جانبي (useEffect) لاختصارات لوحة المفاتيح لوضع التركيز وملء الشاشة
    useEffect(() => {
        const handleKeyPress = (e) => {
            if (e.key === 'F11') {
                e.preventDefault(); // منع السلوك الافتراضي للمتصفح (فتح أدوات المطور)
                toggleFullScreen(); // تبديل وضع ملء الشاشة
            } else if (e.key === 'Escape' && isFullScreen) {
                document.exitFullscreen(); // الخروج من ملء الشاشة عند الضغط على Escape
            }
        };

        document.addEventListener('keydown', handleKeyPress);
        // دالة التنظيف لإزالة المستمع
        return () => document.removeEventListener('keydown', handleKeyPress);
    }, [toggleFullScreen, isFullScreen]); // يعتمد على `toggleFullScreen` و `isFullScreen`

    // تأثير جانبي (useEffect) لجلب بيانات جلسة الاختبار من الخادم
    useEffect(() => {
        const fetchTestSession = async () => {
            // التحقق من وجود توكن المستخدم ومعرف الجلسة
            if (!user?.token || !sessionId) {
                setError('المصادقة مطلوبة');
                setLoading(false);
                return;
            }

            try {
                // إرسال طلب GET مصادق عليه لجلب بيانات جلسة الاختبار
                const data = await authenticatedGet(
                    `/api/tests/session/${sessionId}`,
                    user.token,
                    {},
                    {
                        component: 'TestingPage',
                        operation: 'fetchTestSession'
                    }
                );
                setTestSession(data); // تحديث حالة جلسة الاختبار

                // التحقق مما إذا كان الاختبار قد اكتمل بالفعل
                if (data.status === 'completed') {
                    // إعادة التوجيه إلى صفحة النتائج إذا كان الاختبار مكتملًا
                    navigate('/results', {
                        state: {
                            results: {
                                score: data.score,
                                sessionId: data._id,
                                message: 'الاختبار مكتمل بالفعل'
                            },
                            session: data
                        }
                    });
                    return; // إيقاف التنفيذ
                }

                // تهيئة إجابات المستخدم من بيانات الجلسة (لاستئناف الاختبار)
                const answers = {};
                if (data.userAnswers) {
                    Object.entries(data.userAnswers).forEach(([questionId, answer]) => {
                        answers[questionId] = answer;
                    });
                }
                setUserAnswers(answers); // تحديث إجابات المستخدم المحلية

            } catch (err) {
                setError(err.message || 'فشل تحميل جلسة الاختبار'); // عرض رسالة الخطأ

                // التقاط الخطأ وإرساله إلى Sentry مع السياق
                captureErrorWithContext(err, {
                    context: {
                        component: 'TestingPage',
                        operation: 'fetchTestSession',
                        sessionId,
                        userId: user?._id,
                    },
                });
            } finally {
                setLoading(false); // إنهاء التحميل بغض النظر عن النتيجة
            }
        };

        fetchTestSession(); // استدعاء دالة جلب جلسة الاختبار
    }, [sessionId, user, navigate]); // يعتمد على `sessionId` و `user` و `navigate`

    // تأثير جانبي (useEffect) لتنظيف المؤقت عند إلغاء تحميل المكون أو الانتقال إلى سؤال آخر
    useEffect(() => {
        return () => {
            if (autoAdvanceTimer) {
                clearTimeout(autoAdvanceTimer); // مسح المؤقت إذا كان موجودًا
            }
        };
    }, [autoAdvanceTimer]); // يعتمد على `autoAdvanceTimer`

    /**
     * دالة مساعدة `getCurrentQuestionInfo`.ل
     * تقوم بإرجاع معلومات حول السؤال الرئيسي والسؤال الفرعي الحالي (إذا كان موجودًا).
     * تم استخدام `useCallback` لتحسين الأداء.
     * 
     * @returns {object|null} - كائن يحتوي على معلومات السؤال، أو null إذا لم يكن هناك سؤال.
     */
    const getCurrentQuestionInfo = useCallback(() => {
        if (!testSession?.questions?.[currentQuestionIndex]) return null;

        const currentQuestion = testSession.questions[currentQuestionIndex];
        const isReadingQuestion = currentQuestion.type === 'reading';

        // إذا كان السؤال من نوع "فهم مقروء" ولديه أسئلة فرعية
        if (isReadingQuestion && currentQuestion.questions?.length > 0) {
            const subQuestion = currentQuestion.questions[currentSubQuestionIndex];
            return {
                question: currentQuestion,
                subQuestion: subQuestion,
                isReadingQuestion: true,
                questionKey: `${currentQuestion._id}_${currentSubQuestionIndex}`, // مفتاح مركب للسؤال الفرعي
                totalSubQuestions: currentQuestion.questions.length
            };
        }

        // إذا كان السؤال ليس من نوع "فهم مقروء"
        return {
            question: currentQuestion,
            subQuestion: null,
            isReadingQuestion: false,
            questionKey: currentQuestion._id, // مفتاح السؤال الرئيسي
            totalSubQuestions: 0
        };
    }, [testSession, currentQuestionIndex, currentSubQuestionIndex]); // يعتمد على هذه المتغيرات

    /**
     * دالة `goToNext`.
     * للانتقال إلى السؤال التالي أو السؤال الفرعي التالي.
     * تم استخدام `useCallback` لتحسين الأداء.
     */
    const goToNext = useCallback(() => {
        const questionInfo = getCurrentQuestionInfo();
        if (!questionInfo) return;

        const { isReadingQuestion, totalSubQuestions } = questionInfo;

        // إذا كان السؤال الحالي هو "فهم مقروء" وهناك أسئلة فرعية متبقية
        if (isReadingQuestion && currentSubQuestionIndex < totalSubQuestions - 1) {
            // الانتقال إلى السؤال الفرعي التالي ضمن مقطع القراءة
            setCurrentSubQuestionIndex(currentSubQuestionIndex + 1);
        } else if (currentQuestionIndex < testSession.questions.length - 1) {
            // الانتقال إلى السؤال الرئيسي التالي
            setCurrentQuestionIndex(currentQuestionIndex + 1);
            setCurrentSubQuestionIndex(0); // إعادة تعيين فهرس السؤال الفرعي
        }
    }, [getCurrentQuestionInfo, currentSubQuestionIndex, currentQuestionIndex, testSession]); // يعتمد على هذه المتغيرات

    /**
     * دالة `handleAnswer`.
     * لمعالجة إجابة المستخدم وحفظها محليًا وفي الواجهة الخلفية.
     * تتضمن منطق التغذية الراجعة الفورية والتقدم التلقائي.
     * تم استخدام `useCallback` لتحسين الأداء.
     * 
     * @param {string} choiceKey - مفتاح الخيار الذي اختاره المستخدم.
     */
    const handleAnswer = useCallback(async (choiceKey) => {
        if (!testSession || !user?.token) return;

        // التحقق مما إذا كان الاختبار قد اكتمل بالفعل
        if (testSession.status === 'completed') {
            console.warn('لا يمكن حفظ الإجابة: الاختبار مكتمل بالفعل');
            return;
        }

        const questionInfo = getCurrentQuestionInfo();
        if (!questionInfo) return;

        const { questionKey } = questionInfo;
        const currentQuestion = testSession.questions[currentQuestionIndex];

        // تحديث الحالة المحلية للإجابات فورًا
        setUserAnswers(prev => ({
            ...prev,
            [questionKey]: choiceKey
        }));

        // معالجة التغذية الراجعة الفورية إذا كانت مفعلة
        if (instantFeedbackEnabled) {
            // مسح أي مؤقت موجود
            if (autoAdvanceTimer) {
                clearTimeout(autoAdvanceTimer);
                setAutoAdvanceTimer(null);
            }

            // تحديد الإجابة الصحيحة
            let correctAnswer;
            if (questionInfo.isReadingQuestion && questionInfo.subQuestion) {
                correctAnswer = questionInfo.subQuestion.correct_answer;
            } else {
                correctAnswer = currentQuestion.correct_answer;
            }

            const isCorrect = choiceKey === correctAnswer;
            setFeedbackType(isCorrect ? 'correct' : 'wrong'); // تعيين نوع التغذية الراجعة
            setShowFeedback(true); // إظهار التغذية الراجعة

            // التقدم التلقائي بعد 2 ثانية
            const timer = setTimeout(() => {
                setShowFeedback(false); // إخفاء التغذية الراجعة
                setFeedbackType(null); // مسح نوع التغذية الراجعة
                goToNext(); // الانتقال إلى السؤال التالي
            }, 2000);
            setAutoAdvanceTimer(timer);
        }

        // بالنسبة لأسئلة فهم المقروء، نحتاج إلى حفظ الإجابة بمفتاح مركب
        // الواجهة الخلفية تتوقع معرف السؤال الرئيسي، ولكننا نتعامل مع الأسئلة الفرعية في الواجهة الأمامية
        const saveKey = questionInfo.isReadingQuestion
            ? `${currentQuestion._id}_sub_${currentSubQuestionIndex}`
            : currentQuestion._id;

        // حفظ الإجابة في الواجهة الخلفية
        try {
            await authenticatedPut(
                `/api/tests/session/${sessionId}/answer`,
                user.token,
                {
                    questionId: saveKey,
                    answer: choiceKey
                },
                {},
                {
                    component: 'TestingPage',
                    operation: 'saveAnswer'
                }
            );
        } catch (err) {
            // معالجة حالات الخطأ المحددة
            if (err.status === 400) {
                console.warn('فشل حفظ الإجابة: قد يكون الاختبار مكتملًا');
                // يمكن تحديث بيانات جلسة الاختبار أو إعادة التوجيه إلى صفحة النتائج هنا
            } else {
                // فشل صامت - حفظ الإجابة ليس حرجًا لتجربة المستخدم
                // يمكن تطبيق منطق إعادة المحاولة أو إظهار إشعار هنا
                console.warn('خطأ في حفظ الإجابة:', err.message);
            }
        }
    }, [testSession, currentQuestionIndex, currentSubQuestionIndex, sessionId, user, getCurrentQuestionInfo, instantFeedbackEnabled, autoAdvanceTimer, goToNext]); // يعتمد على هذه المتغيرات

    /**
     * دالة `goToPrev`.
     * للانتقال إلى السؤال السابق أو السؤال الفرعي السابق.
     * تم استخدام `useCallback` لتحسين الأداء.
     */
    const goToPrev = useCallback(() => {
        const questionInfo = getCurrentQuestionInfo();
        if (!questionInfo) return;

        // إذا كان هناك سؤال فرعي سابق
        if (currentSubQuestionIndex > 0) {
            // الانتقال إلى السؤال الفرعي السابق ضمن مقطع القراءة
            setCurrentSubQuestionIndex(currentSubQuestionIndex - 1);
        } else if (currentQuestionIndex > 0) {
            // الانتقال إلى السؤال الرئيسي السابق
            const prevQuestionIndex = currentQuestionIndex - 1;
            const prevQuestion = testSession.questions[prevQuestionIndex];

            setCurrentQuestionIndex(prevQuestionIndex);

            // إذا كان السؤال السابق هو "فهم مقروء"، انتقل إلى آخر سؤال فرعي فيه
            if (prevQuestion.type === 'reading' && prevQuestion.questions?.length > 0) {
                setCurrentSubQuestionIndex(prevQuestion.questions.length - 1);
            } else {
                setCurrentSubQuestionIndex(0); // إعادة تعيين فهرس السؤال الفرعي
            }
        }
    }, [getCurrentQuestionInfo, currentSubQuestionIndex, currentQuestionIndex, testSession]); // يعتمد على هذه المتغيرات

    /**
     * دالة `isLastQuestion`.ل
     * تتحقق مما إذا كان السؤال الحالي هو آخر سؤال (أو سؤال فرعي) في الاختبار.
     * تم استخدام `useCallback` لتحسين الأداء.
     * 
     * @returns {boolean} - true إذا كان السؤال الحالي هو الأخير، وإلا false.
     */
    const isLastQuestion = useCallback(() => {
        const questionInfo = getCurrentQuestionInfo();
        if (!questionInfo) return true;

        const { isReadingQuestion, totalSubQuestions } = questionInfo;
        const isLastMainQuestion = currentQuestionIndex === testSession.questions.length - 1;

        if (isReadingQuestion) {
            const isLastSubQuestion = currentSubQuestionIndex === totalSubQuestions - 1;
            return isLastMainQuestion && isLastSubQuestion;
        }

        return isLastMainQuestion;
    }, [getCurrentQuestionInfo, currentQuestionIndex, currentSubQuestionIndex, testSession]); // يعتمد على هذه المتغيرات

    /**
     * دالة `isFirstQuestion`.ل
     * تتحقق مما إذا كان السؤال الحالي هو أول سؤال (أو سؤال فرعي) في الاختبار.
     * تم استخدام `useCallback` لتحسين الأداء.
     * 
     * @returns {boolean} - true إذا كان السؤال الحالي هو الأول، وإلا false.
     */
    const isFirstQuestion = useCallback(() => {
        return currentQuestionIndex === 0 && currentSubQuestionIndex === 0;
    }, [currentQuestionIndex, currentSubQuestionIndex]); // يعتمد على هذه المتغيرات

    /**
     * دالة `finishTest`.ل
     * لتقديم الاختبار إلى الواجهة الخلفية والحصول على النتائج.
     * تم استخدام `useCallback` لتحسين الأداء.
     */
    const finishTest = useCallback(async () => {
        // التحقق مما إذا كان الاختبار قد اكتمل بالفعل
        if (testSession?.status === 'completed') {
            console.warn('لا يمكن تقديم الاختبار: الاختبار مكتمل بالفعل');
            navigate('/results', {
                state: {
                    results: {
                        score: testSession.score,
                        sessionId: testSession._id,
                        message: 'الاختبار مكتمل بالفعل'
                    },
                    session: testSession
                }
            });
            return;
        }

        setIsSubmitting(true); // تعيين حالة التقديم إلى true
        try {
            // إرسال طلب POST مصادق عليه لتقديم الاختبار والحصول على النتائج
            const results = await authenticatedPost(
                `/api/tests/session/${sessionId}/submit`,
                user.token,
                null, // لا يوجد جسم للطلب
                {},
                {
                    component: 'TestingPage',
                    operation: 'submitTest'
                }
            );

            // الانتقال إلى صفحة النتائج مع بيانات النتائج والجلسة
            navigate('/results', { state: { results, session: testSession } });

        } catch (err) {
            // معالجة حالات الخطأ المحددة
            if (err.status === 400) {
                // الاختبار تم تقديمه بالفعل - إعادة التوجيه إلى النتائج
                console.warn('الاختبار تم تقديمه بالفعل، جاري إعادة التوجيه إلى النتائج');
                navigate('/results', {
                    state: {
                        results: {
                            score: testSession?.score || 0,
                            sessionId: testSession?._id,
                            message: 'الاختبار تم تقديمه بالفعل'
                        },
                        session: testSession
                    }
                });
                return;
            }

            setError('فشل تقديم الاختبار. يرجى المحاولة مرة أخرى.'); // عرض رسالة الخطأ

            // التقاط الخطأ وإرساله إلى Sentry
            captureErrorWithContext(err, {
                context: {
                    component: 'TestingPage',
                    operation: 'finishTest',
                    sessionId,
                    userId: user?._id,
                },
            });
        } finally {
            setIsSubmitting(false); // إعادة تعيين حالة التقديم
        }
    }, [sessionId, user, navigate, testSession]); // يعتمد على هذه المتغيرات

    // عرض شاشة التحميل
    if (loading) {
        return (
            <div className="dashboard-container">
                <div className="loading">
                    <div className="spinner"></div>
                    <span>جاري تحميل جلسة الاختبار...</span>
                </div>
            </div>
        );
    }

    // عرض رسالة الخطأ
    if (error) {
        return (
            <div className="dashboard-container">
                <div className="card text-center">
                    <h3 className="text-error mb-md">خطأ</h3>
                    <p className="mb-lg">{error}</p>
                    <button onClick={() => navigate('/dashboard')} className="btn btn-primary">
                        العودة إلى لوحة التحكم
                    </button>
                </div>
            </div>
        );
    }

    // عرض رسالة إذا لم تكن هناك أسئلة متاحة في الجلسة
    if (!testSession || !testSession.questions || testSession.questions.length === 0) {
        return (
            <div className="dashboard-container">
                <div className="card text-center">
                    <h3 className="mb-md">لا توجد أسئلة متاحة</h3>
                    <p className="mb-lg">جلسة الاختبار هذه لا تحتوي على أسئلة.</p>
                    <button onClick={() => navigate('/dashboard')} className="btn btn-primary">
                        العودة إلى لوحة التحكم
                    </button>
                </div>
            </div>
        );
    }

    // الحصول على معلومات السؤال الحالي وحساب التقدم
    const questionInfo = getCurrentQuestionInfo();
    const currentQuestion = questionInfo?.question;
    const currentSubQuestion = questionInfo?.subQuestion;

    /**
     * دالة `calculateProgress`.ل
     * تحسب نسبة التقدم الكلية في الاختبار، مع الأخذ في الاعتبار الأسئلة الفرعية.
     * 
     * @returns {number} - نسبة التقدم المئوية (من 0 إلى 100).
     */
    const calculateProgress = () => {
        if (!testSession?.questions) return 0;

        let totalQuestions = 0;
        let completedQuestions = 0;

        // حساب العدد الكلي للأسئلة بما في ذلك الأسئلة الفرعية
        testSession.questions.forEach(q => {
            if (q.type === 'reading' && q.questions?.length > 0) {
                totalQuestions += q.questions.length;
            } else {
                totalQuestions += 1;
            }
        });

        // حساب الأسئلة المكتملة حتى الموضع الحالي
        for (let i = 0; i < currentQuestionIndex; i++) {
            const q = testSession.questions[i];
            if (q.type === 'reading' && q.questions?.length > 0) {
                completedQuestions += q.questions.length;
            } else {
                completedQuestions += 1;
            }
        }

        // إضافة تقدم السؤال الفرعي الحالي
        if (questionInfo?.isReadingQuestion) {
            completedQuestions += currentSubQuestionIndex;
        }

        // إضافة 1 للسؤال الحالي الذي يتم العمل عليه
        completedQuestions += 1;

        // التأكد من أن التقدم لا يتجاوز 100%
        return Math.min((completedQuestions / totalQuestions) * 100, 100);
    };

    const progress = calculateProgress(); // نسبة التقدم المحسوبة
    // الإجابة الحالية للسؤال الحالي
    const currentAnswer = questionInfo ? userAnswers[questionInfo.questionKey] : null;

    return (
        // تخطيط صفحة الاختبار، مع فئات CSS لوضع التركيز وملء الشاشة
        <div className={`testing-layout ${focusMode ? 'focus-mode' : ''} ${isFullScreen ? 'fullscreen-mode' : ''}`}>
            <div className="testing-container">
                {/* رأس التقدم المحسن مع عناصر التحكم في التركيز */}
                <div className="testing-progress-header">
                    <div className="progress-info">
                        {/* عداد الأسئلة */}
                        <span className="question-counter">
                            {currentQuestionIndex + 1} / {testSession.questions.length}
                            {questionInfo?.isReadingQuestion && questionInfo.totalSubQuestions > 1 && (
                                <span className="sub-question-counter">
                                    {' '}({currentSubQuestionIndex + 1}/{questionInfo.totalSubQuestions})
                                </span>
                            )}
                        </span>
                        {/* شريط التقدم البسيط */}
                        <div className="progress-bar-minimal">
                            <div className="progress-bar-fill" style={{ width: `${progress}%` }}></div>
                        </div>
                    </div>

                    <div className="header-controls">
                        {/* مؤشر نوع السؤال (لفهم المقروء) */}
                        {questionInfo?.isReadingQuestion && (
                            <div className="question-type-indicator">
                                فهم مقروء
                            </div>
                        )}

                        {/* عناصر التحكم في وضع التركيز وملء الشاشة */}
                        <div className="focus-controls">
                            {/* زر تبديل وضع التركيز */}
                            <button
                                onClick={() => setFocusMode(!focusMode)}
                                className={`focus-btn ${focusMode ? 'active' : ''}`}
                                title="تبديل وضع التركيز"
                                aria-label="تبديل وضع التركيز"
                            >
                                {focusMode ? '👁️' : '🎯'}
                            </button>

                            {/* زر تبديل ملء الشاشة */}
                            <button
                                onClick={toggleFullScreen}
                                className={`fullscreen-btn ${isFullScreen ? 'active' : ''}`}
                                title="تبديل ملء الشاشة (F11)"
                                aria-label="تبديل ملء الشاشة"
                            >
                                {isFullScreen ? '🗗' : '⛶'}
                            </button>
                        </div>
                    </div>
                </div>

                {/* محتوى السؤال الرئيسي - منطقة التركيز */}
                <div className="testing-main-content">
                    <div className="question-card-focus">
                        {/* مكون QuestionRenderer لعرض السؤال الحالي */}
                        <QuestionRenderer
                            question={currentQuestion}
                            subQuestion={currentSubQuestion}
                            subQuestionIndex={currentSubQuestionIndex}
                            selectedAnswer={currentAnswer}
                            onAnswer={handleAnswer}
                            hoveredOption={hoveredOption}
                            setHoveredOption={setHoveredOption}
                            expandedPassage={expandedPassage}
                            setExpandedPassage={setExpandedPassage}
                            showFeedback={showFeedback}
                            feedbackType={feedbackType}
                            instantFeedbackEnabled={instantFeedbackEnabled}
                        />
                    </div>
                </div>

                {/* تذييل التنقل البسيط */}
                <div className="testing-navigation">
                    {/* زر السؤال السابق */}
                    <button
                        onClick={goToPrev}
                        disabled={isFirstQuestion()} // تعطيل الزر إذا كان السؤال هو الأول
                        className="nav-btn-focus prev"
                        aria-label="السؤال السابق"
                    >
                        ← السابق
                    </button>

                    {/* منطقة الأزرار المركزية (التالي أو تقديم الاختبار) */}
                    <div className="nav-center">
                        {!isLastQuestion() ? (
                            // زر السؤال التالي
                            <button onClick={goToNext} className="nav-btn-focus next">
                                {questionInfo?.isReadingQuestion && currentSubQuestionIndex < questionInfo.totalSubQuestions - 1
                                    ? 'الجزء التالي ←' // نص خاص للأسئلة الفرعية
                                    : 'السؤال التالي ←'
                                }
                            </button>
                        ) : (
                            // زر تقديم الاختبار (يظهر فقط في آخر سؤال)ل
                            <button
                                onClick={finishTest}
                                disabled={isSubmitting} // تعطيل الزر أثناء التقديم
                                className="nav-btn-focus submit"
                            >
                                {isSubmitting ? (
                                    <>
                                        <div className="spinner-small"></div>
                                        جاري التقديم...
                                    </>
                                ) : (
                                    'تقديم الاختبار ✓'
                                )}
                            </button>
                        )}
                    </div>

                    {/* مسافة فارغة للمحاذاة */}
                    <div className="nav-spacer"></div>
                </div>
            </div>
        </div>
    );
};

/**
 * مكون QuestionRenderer (عارض الأسئلة).ل
 * يتعامل مع عرض جميع أنواع الأسئلة ديناميكيًا.
 * تم تحسينه باستخدام `React.memo` لمنع إعادة العرض غير الضرورية.
 * 
 * @param {object} props - خصائص المكون.
 * @param {object} props.question - كائن السؤال الرئيسي.
 * @param {object} props.subQuestion - كائن السؤال الفرعي (لفهم المقروء).
 * @param {number} props.subQuestionIndex - فهرس السؤال الفرعي الحالي.
 * @param {string} props.selectedAnswer - مفتاح الإجابة المختارة حاليًا.
 * @param {function} props.onAnswer - دالة رد نداء عند اختيار المستخدم لإجابة.
 * @param {string} props.hoveredOption - مفتاح الخيار الذي يتم التحويم عليه حاليًا.
 * @param {function} props.setHoveredOption - دالة لتعيين الخيار الذي يتم التحويم عليه.
 * @param {boolean} props.expandedPassage - ما إذا كان مقطع القراءة موسعًا.
 * @param {function} props.setExpandedPassage - دالة لتعيين حالة توسيع مقطع القراءة.
 * @param {boolean} props.showFeedback - ما إذا كان يجب إظهار التغذية الراجعة.
 * @param {string} props.feedbackType - نوع التغذية الراجعة ('correct' أو 'wrong').
 * @param {boolean} props.instantFeedbackEnabled - ما إذا كانت التغذية الراجعة الفورية مفعلة.
 * @returns {JSX.Element} - عرض السؤال مع خياراته.
 */
const QuestionRenderer = memo(({
    question,
    subQuestion,
    subQuestionIndex,
    selectedAnswer,
    onAnswer,
    hoveredOption,
    setHoveredOption,
    expandedPassage,
    setExpandedPassage,
    showFeedback,
    feedbackType,
    instantFeedbackEnabled
}) => {
    const questionType = question?.type; // نوع السؤال

    // تحديد الخيارات النشطة: إذا كان السؤال فهم مقروء، استخدم خيارات السؤال الفرعي، وإلا استخدم خيارات السؤال الرئيسي
    const activeOptions = questionType === 'reading' && subQuestion ? subQuestion.options : question?.options;

    /**
     * دالة مساعدة `renderOptionCard`.ل
     * تقوم بعرض بطاقة خيار الإجابة.
     * 
     * @param {string} optionKey - مفتاح الخيار.
     * @param {object} optionData - بيانات الخيار (مثل النص).
     * @param {boolean} isSelected - ما إذا كان هذا الخيار هو الإجابة المختارة.
     * @returns {JSX.Element} - بطاقة خيار الإجابة.
     */
    const renderOptionCard = (optionKey, optionData, isSelected) => {
        // تحديد الإجابة الصحيحة للتغذية الراجعة
        let correctAnswer;
        if (questionType === 'reading' && subQuestion) {
            correctAnswer = subQuestion.correct_answer;
        } else {
            correctAnswer = question?.correct_answer;
        }

        // تحديد فئة التغذية الراجعة للتنسيق
        let feedbackClass = '';
        if (instantFeedbackEnabled && showFeedback) {
            if (isSelected) {
                feedbackClass = feedbackType === 'correct' ? 'feedback-correct' : 'feedback-wrong';
            } else if (optionKey === correctAnswer) {
                feedbackClass = 'feedback-correct-answer'; // تمييز الإجابة الصحيحة حتى لو لم يختارها المستخدم
            }
        }

        return (
            <div
                key={optionKey}
                className={`option ${isSelected ? 'selected' : ''} ${feedbackClass}`}
                onClick={() => !showFeedback && onAnswer(optionKey)} // تعطيل النقر إذا كانت التغذية الراجعة معروضة
                onMouseEnter={() => !showFeedback && setHoveredOption(optionKey)} // تعيين الخيار المحوم عليه
                onMouseLeave={() => !showFeedback && setHoveredOption(null)} // مسح الخيار المحوم عليه
                aria-label={`الخيار ${optionKey}: ${optionData.text}`}
                role="button"
                tabIndex={0}
                style={{ cursor: showFeedback ? 'default' : 'pointer' }} // تغيير المؤشر بعد الإجابة
            >
                <span className="option-text">{optionData.text}</span>
            </div>
        );
    };

    /**
     * دالة مساعدة `renderHighlightedText`.ل
     * تقوم بتمييز الكلمات في أسئلة "اكتشاف الخطأ" عند التحويم.
     * 
     * @param {string} text - نص السؤال.
     * @param {object} options - خيارات السؤال.
     * @returns {JSX.Element} - نص السؤال مع الكلمات المميزة.
     */
    const renderHighlightedText = (text, options) => {
        if (questionType !== 'error') return text; // تعمل فقط لأسئلة "اكتشاف الخطأ"

        let highlightedText = text;
        Object.entries(options).forEach(([key, option]) => {
            const isHovered = hoveredOption === key;
            if (isHovered && text.includes(option.text)) {
                // استبدال الكلمة بنسخة مميزة بـ HTML
                highlightedText = highlightedText.replace(
                    option.text,
                    `<span class="highlighted-word hover-highlight">${option.text}</span>`
                );
            }
        });

        // استخدام dangerouslySetInnerHTML لعرض HTML المولد
        return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />;
    };

    /**
     * دالة مساعدة `renderCompletionPreview`.ل
     * تقوم بعرض معاينة للجملة المكتملة في أسئلة "إكمال الجملة" عند التحويم على الخيارات.
     * 
     * @param {string} text - نص السؤال الأصلي.
     * @param {object} options - خيارات الإكمال.
     * @returns {string} - نص الجملة مع معاينة الإكمال.
     */
    const renderCompletionPreview = (text, options) => {
        if (questionType !== 'completion') return text; // تعمل فقط لأسئلة "إكمال الجملة"

        let previewText = text;
        if (hoveredOption && options[hoveredOption]) {
            // استبدال العنصر النائب (مثل "...") بالخيار الذي يتم التحويم عليه
            previewText = text.replace(/\.{3,}|_+/g, options[hoveredOption].text);
        }

        return previewText;
    };

    /**
     * دالة مساعدة `isPassageCollapsible`.ل
     * تتحقق مما إذا كان مقطع القراءة قابلاً للطي (أكثر من 3 أسطر).
     * 
     * @param {string} passage - نص مقطع القراءة.
     * @returns {boolean} - true إذا كان قابلاً للطي، وإلا false.
     */
    const isPassageCollapsible = (passage) => {
        return passage && passage.split('\n').length > 3;
    };

    /**
     * دالة مساعدة `getTruncatedPassage`.ل
     * تقوم بإرجاع أول 3 أسطر من مقطع القراءة.
     * 
     * @param {string} passage - نص مقطع القراءة.
     * @returns {string} - المقطع المختصر.
     */
    const getTruncatedPassage = (passage) => {
        const lines = passage.split('\n');
        return lines.slice(0, 3).join('\n');
    };

    return (
        <div className="question-renderer">
            {/* مقطع القراءة (لفهم المقروء) */}
            {questionType === 'reading' && question.passage && (
                <div className="mb-lg">
                    <h4 className="mb-md">مقطع القراءة</h4>
                    <div
                        className={`passage-preview ${!expandedPassage && isPassageCollapsible(question.passage) ? 'collapsed' : ''}`}
                        onClick={() => isPassageCollapsible(question.passage) && setExpandedPassage(!expandedPassage)}
                        style={{ cursor: isPassageCollapsible(question.passage) ? 'pointer' : 'default' }}
                    >
                        <div className="passage-preview-content">
                            {expandedPassage || !isPassageCollapsible(question.passage)
                                ? question.passage // عرض المقطع كاملاً إذا كان موسعًا أو غير قابل للطي
                                : getTruncatedPassage(question.passage) // عرض المقطع المختصر
                            }
                        </div>
                    </div>
                </div>
            )}

            {/* نص السؤال */}
            <div className="mb-lg">
                <div className="flex justify-between items-center mb-md">
                    <h2>
                        {/* عرض نص توضيحي بناءً على نوع السؤال */}
                        {questionType === 'analogy' && 'ابحث عن الزوج الذي له نفس العلاقة:'}
                        {questionType === 'completion' && 'أكمل الجملة:'}
                        {questionType === 'error' && 'حدد الكلمة التي لا تتناسب مع السياق:'}
                        {questionType === 'odd_one_out' && 'اختر الكلمة الشاذة:'}
                        {questionType === 'reading' && subQuestion && 'أجب عن السؤال بناءً على المقطع:'}
                    </h2>
                    {/* زر الإشارة المرجعية */}
                    <BookmarkButton
                        questionId={question?._id || question?.id}
                        onClick={(questionId, isBookmarked) => {
                            // مكان لوظيفة الإشارة المرجعية
                            // TODO: تنفيذ وظيفة الإشارة المرجعية مع API الواجهة الخلفية
                        }}
                    />
                </div>

                {/* محتوى نص السؤال الفعلي */}
                <div className="question-content-text">
                    {questionType === 'completion' ? (
                        <p className="text-lg">
                            {renderCompletionPreview(question.question_text, question.options)}
                        </p>
                    ) : questionType === 'error' ? (
                        <p className="text-lg">
                            {renderHighlightedText(question.question_text, question.options)}
                        </p>
                    ) : questionType === 'reading' && subQuestion ? (
                        <div>
                            <p className="text-lg">{subQuestion.question_text}</p>
                            {subQuestionIndex !== undefined && question.questions?.length > 1 && (
                                <div className="text-secondary mt-sm">
                                    سؤال {subQuestionIndex + 1} من {question.questions.length}
                                </div>
                            )}
                        </div>
                    ) : (
                        <p className="text-lg">{question?.question_text}</p>
                    )}
                </div>
            </div>

            {/* خيارات الإجابة */}
            <div className="options-grid">
                {activeOptions && Object.entries(activeOptions).map(([key, option]) =>
                    renderOptionCard(key, option, selectedAnswer === key)
                )}
            </div>
        </div>
    );
});

// تصدير المكون الرئيسي لصفحة الاختبار
export default TestingPage;
