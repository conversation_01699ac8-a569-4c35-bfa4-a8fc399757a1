// استيراد المكتبات اللازمة: React للتعامل مع الحالة، و styled-components لتنسيق المكون
import React, { useState } from 'react';
import styled from 'styled-components';

/**
 * مكون زر الإشارة المرجعية (BookmarkButton).
 * يعرض زرًا يمكن للمستخدم النقر عليه لإضافة أو إزالة سؤال من الإشارات المرجعية.
 * 
 * @param {object} props - الخصائص المستلمة.
 * @param {string} props.questionId - معرف السؤال المرتبط بالزر.
 * @param {boolean} [props.isBookmarked=false] - الحالة الأولية للزر (هل هو مُعلَّم أم لا).
 * @param {function} props.onClick - دالة يتم استدعاؤها عند النقر على الزر، وتستقبل معرف السؤال والحالة الجديدة.
 * @returns {JSX.Element} - زر الإشارة المرجعية المصمم.
 */
const BookmarkButton = ({ questionId, isBookmarked: initialBookmarked = false, onClick }) => {
  // استخدام حالة محلية لتتبع ما إذا كانت الإشارة المرجعية نشطة أم لا
  const [isBookmarked, setIsBookmarked] = useState(initialBookmarked);

  // دالة لمعالجة النقر على الزر
  const handleClick = () => {
    // عكس الحالة الحالية للإشارة المرجعية
    const newBookmarkState = !isBookmarked;
    setIsBookmarked(newBookmarkState);

    // استدعاء الدالة onClick (إذا تم توفيرها) مع معرف السؤال والحالة الجديدة
    if (onClick) {
      onClick(questionId, newBookmarkState);
    }
  };

  // عرض المكون
  return (
    <StyledWrapper>
      <label className="ui-bookmark">
        {/* حقل الإدخال (checkbox) المخفي الذي يتحكم في الحالة */}
        <input
          type="checkbox"
          checked={isBookmarked}
          onChange={handleClick}
          aria-label={isBookmarked ? 'إزالة الإشارة المرجعية' : 'إضافة إشارة مرجعية'}
        />
        {/* العنصر الذي يعرض أيقونة الإشارة المرجعية */}
        <div className="bookmark">
          <svg viewBox="0 0 32 32">
            <g>
              <path d="M27 4v27a1 1 0 0 1-1.625.781L16 24.281l-9.375 7.5A1 1 0 0 1 5 31V4a4 4 0 0 1 4-4h14a4 4 0 0 1 4 4z" />
            </g>
          </svg>
        </div>
      </label>
    </StyledWrapper>
  );
};

// استخدام styled-components لإنشاء حاوية منسقة للمكون
const StyledWrapper = styled.div`
  /* --- بداية تنسيقات زر الإشارة المرجعية --- */

  .ui-bookmark {
    /* متغيرات CSS لتسهيل التحكم في التصميم */
    --icon-size: 24px; /* حجم الأيقونة */
    --icon-secondary-color: rgb(77, 77, 77); /* اللون الثانوي (الافتراضي) */
    --icon-hover-color: rgb(97, 97, 97); /* اللون عند مرور المؤشر */
    --icon-primary-color: gold; /* اللون الأساسي (عند التفعيل) */
    --icon-circle-border: 1px solid var(--icon-primary-color); /* حدود الدائرة المتحركة */
    --icon-circle-size: 35px; /* حجم الدائرة المتحركة */
    --icon-anmt-duration: 0.3s; /* مدة الحركة */
  }

  /* إخفاء مربع الاختيار الافتراضي للمتصفح */
  .ui-bookmark input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: none;
  }

  /* تنسيق أيقونة الإشارة المرجعية نفسها */
  .ui-bookmark .bookmark {
    width: var(--icon-size);
    height: auto;
    fill: var(--icon-secondary-color); /* استخدام اللون الثانوي كلون افتراضي */
    cursor: pointer;
    transition: 0.2s;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transform-origin: top;
  }

  /* إنشاء تأثير الدوائر المتناثرة عند التفعيل باستخدام عنصر زائف */
  .bookmark::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    /* استخدام box-shadow لإنشاء 8 دوائر حول الأيقونة */
    box-shadow: 0 30px 0 -4px var(--icon-primary-color),
      30px 0 0 -4px var(--icon-primary-color),
      0 -30px 0 -4px var(--icon-primary-color),
      -30px 0 0 -4px var(--icon-primary-color),
      -22px 22px 0 -4px var(--icon-primary-color),
      -22px -22px 0 -4px var(--icon-primary-color),
      22px -22px 0 -4px var(--icon-primary-color),
      22px 22px 0 -4px var(--icon-primary-color);
    border-radius: 50%;
    transform: scale(0); /* إخفاء الدوائر في الحالة الافتراضية */
  }

  /* إنشاء تأثير الدائرة المتوسعة عند التفعيل */
  .bookmark::before {
    content: "";
    position: absolute;
    border-radius: 50%;
    border: var(--icon-circle-border);
    opacity: 0; /* إخفاء الدائرة في الحالة الافتراضية */
  }

  /* --- بداية تعريف الحركات والتفاعلات --- */

  /* تغيير لون الأيقونة عند مرور مؤشر الفأرة */
  .ui-bookmark:hover .bookmark {
    fill: var(--icon-hover-color);
  }

  /* عند تحديد مربع الاختيار (النقر)، يتم تفعيل حركة الدوائر المتناثرة */
  .ui-bookmark input:checked + .bookmark::after {
    animation: circles var(--icon-anmt-duration) cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    animation-delay: var(--icon-anmt-duration);
  }

  /* تغيير لون الأيقونة وتطبيق حركة عليها عند التفعيل */
  .ui-bookmark input:checked + .bookmark {
    fill: var(--icon-primary-color);
    animation: bookmark var(--icon-anmt-duration) forwards;
    transition-delay: 0.3s;
  }

  /* تفعيل حركة الدائرة المتوسعة عند التفعيل */
  .ui-bookmark input:checked + .bookmark::before {
    animation: circle var(--icon-anmt-duration) cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    animation-delay: var(--icon-anmt-duration);
  }

  /* --- بداية تعريف الحركات (keyframes) --- */

  /* حركة الأيقونة نفسها (تأثير الضغط) */
  @keyframes bookmark {
    50% {
      transform: scaleY(0.6);
    }
    100% {
      transform: scaleY(1);
    }
  }

  /* حركة الدائرة المتوسعة */
  @keyframes circle {
    from {
      width: 0;
      height: 0;
      opacity: 0;
    }
    90% {
      width: var(--icon-circle-size);
      height: var(--icon-circle-size);
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  /* حركة الدوائر المتناثرة */
  @keyframes circles {
    from {
      transform: scale(0);
    }
    40% {
      opacity: 1;
    }
    to {
      transform: scale(0.8);
      opacity: 0;
    }
  }
`;

// تصدير المكون لاستخدامه في التطبيق
export default BookmarkButton;