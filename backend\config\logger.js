// backend/config/logger.js

// استيراد مكتبة winston لإدارة التسجيل (logging)
const winston = require('winston');

// استيراد وظائف التنسيق من winston
const { combine, timestamp, json, colorize, align, printf } = winston.format;

// تعريف مستويات الأهمية المخصصة للسجلات (logs)
// كل مستوى له قيمة رقمية تحدد أولويته (الأقل رقمًا هو الأكثر أهمية)
const levels = {
  error: 0, // الأخطاء الحرجة
  warn: 1,  // التحذيرات
  info: 2,  // معلومات عامة
  http: 3,  // طلبات HTTP
  debug: 4, // رسائل التصحيح التفصيلية
};

/**
 * دالة `level`.
 * تحدد مستوى التسجيل النشط بناءً على بيئة التشغيل.
 * 
 * @returns {string} - مستوى التسجيل ('debug' في التطوير، 'warn' في الإنتاج).
 */
const level = () => {
  const env = process.env.NODE_ENV || 'development'; // الحصول على بيئة التشغيل
  const isDevelopment = env === 'development'; // التحقق مما إذا كانت البيئة تطويرية
  return isDevelopment ? 'debug' : 'warn'; // في التطوير سجل كل شيء، في الإنتاج سجل التحذيرات والأخطاء فقط
};

// تعريف الألوان لكل مستوى تسجيل عند العرض في الطرفية (console)
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// إضافة الألوان المخصصة إلى winston
winston.addColors(colors);

// إعداد مُسجِّل winston
const logger = winston.createLogger({
  level: level(), // تعيين المستوى الأدنى للسجلات التي سيتم معالجتها
  levels, // استخدام المستويات المخصصة التي تم تعريفها
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }), // إضافة طابع زمني للسجل
    json() // تنسيق السجل كـ JSON (مفيد لملفات السجل)
  ),
  transports: [
    // ناقل (transport) لتسجيل الأخطاء فقط في ملف 'error.log'
    new winston.transports.File({
      filename: 'error.log',
      level: 'error',
    }),
    // ناقل لتسجيل جميع السجلات (بالمستوى المحدد أو أعلى) في ملف 'combined.log'
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});

// إذا كنا في وضع التطوير، أضف ناقلًا إضافيًا لعرض السجلات في الطرفية (console)
if (process.env.NODE_ENV !== 'production') {
  logger.add(
    new winston.transports.Console({
      format: combine(
        colorize({ all: true }), // تلوين السجلات في الطرفية
        align(), // محاذاة السجلات
        // تنسيق مخصص لعرض السجلات في الطرفية
        printf((info) => `[${info.timestamp}] ${info.level}: ${info.message}`)
      ),
    })
  );
}

// تصدير كائن المُسجِّل لاستخدامه في أجزاء أخرى من التطبيق
module.exports = logger;
