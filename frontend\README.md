# الواجهة الأمامية للتطبيق (Frontend Application)

يمثل هذا المجلد الواجهة الأمامية لتطبيق "Tumrah Testing Platform"، وهو مبني باستخدام React.js وVite.js. يوفر هذا الجزء من التطبيق واجهة المستخدم التفاعلية التي يتفاعل معها المستخدمون، بما في ذلك صفحات الاختبار، لوحة التحكم، وإدارة المستخدمين (للمديرين).

## هيكل المجلد

يتكون هذا المجلد من عدة مجلدات فرعية رئيسية، كل منها مسؤول عن جانب معين من جوانب الواجهة الأمامية:

*   **`public/`**: يحتوي على الأصول الثابتة التي يتم تقديمها مباشرة بواسطة الخادم، مثل ملفات HTML الأساسية والأيقونات.
*   **`src/`**: يحتوي على الكود المصدري الرئيسي لتطبيق React. هذا هو المكان الذي توجد فيه معظم المكونات، الصفحات، السياقات، والأدوات المساعدة.
    *   **`src/assets/`**: أصول ثابتة مثل الصور والأيقونات.
    *   **`src/components/`**: مكونات React قابلة لإعادة الاستخدام.
    *   **`src/config/`**: ملفات الإعدادات والتكوينات.
    *   **`src/context/`**: سياقات React لإدارة الحالة العامة.
    *   **`src/pages/`**: المكونات الرئيسية التي تمثل صفحات التطبيق.
    *   **`src/utils/`**: دوال مساعدة وأدوات عامة.
*   **`node_modules/`**: يحتوي على جميع الحزم والتبعيات المثبتة للمشروع.
*   **ملفات الإعدادات**: مثل `package.json`، `vite.config.js`، `postcss.config.js`، وغيرها.

## التقنيات المستخدمة

*   **React.js**: مكتبة JavaScript لبناء واجهات المستخدم.
*   **Vite.js**: أداة بناء سريعة لتطوير الويب الحديث.
*   **React Router**: لإدارة التوجيه والتنقل داخل التطبيق.
*   **Context API**: لإدارة الحالة العامة للتطبيق.
*   **Sentry**: لمراقبة الأخطاء والأداء.
*   **Chart.js**: لعرض الرسوم البيانية (في صفحة النتائج).
*   **Styled Components**: لتنسيق المكونات (في بعض المكونات).

## كيفية التشغيل

للتشغيل المحلي للواجهة الأمامية، تأكد من تثبيت Node.js و npm (أو yarn).

1.  **انتقل إلى مجلد الواجهة الأمامية:**
    ```bash
    cd frontend
    ```
2.  **تثبيت التبعيات:**
    ```bash
    npm install
    # أو
    yarn install
    ```
3.  **تشغيل خادم التطوير:**
    ```bash
    npm run dev
    # أو
    yarn dev
    ```
    سيتم تشغيل التطبيق عادةً على `http://localhost:5173` (أو منفذ آخر متاح).

## الوظائف الرئيسية

*   **مصادقة المستخدم**: تسجيل الدخول عبر تليجرام.
*   **لوحة التحكم**: عرض إحصائيات المستخدم وبدء الاختبارات.
*   **صفحة الاختبار**: تجربة اختبار تفاعلية مع أنواع أسئلة مختلفة.
*   **صفحة النتائج**: عرض نتائج الاختبار وتحليلات الأداء.
*   **إدارة الأسئلة (للمديرين)**: إضافة، تعديل، وحذف الأسئلة.
*   **تبديل المظهر**: التبديل بين الوضع الفاتح والداكن.