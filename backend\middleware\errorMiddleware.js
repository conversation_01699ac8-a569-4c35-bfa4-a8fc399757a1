// backend/middleware/errorMiddleware.js

// استيراد مُسجِّل winston لتسجيل الأخطاء في الملفات
const logger = require('../config/logger');
// استيراد دالة Sentry لالتقاط الأخطاء مع السياق
const { captureErrorWithContext } = require('../config/sentry');

/**
 * دالة `notFound`.
 * middleware لمعالجة المسارات غير الموجودة (404 Not Found).
 * يتم وضعها عادةً بعد جميع تعريفات المسارات في التطبيق.
 * 
 * @param {object} req - كائن الطلب من Express.
 * @param {object} res - كائن الاستجابة من Express.
 * @param {function} next - دالة رد النداء للانتقال إلى middleware التالي.
 * @returns {void} - تمرر خطأ إلى middleware معالجة الأخطاء.
 */
const notFound = (req, res, next) => {
    // إنشاء كائن خطأ جديد مع رسالة توضح أن المسار غير موجود
    const error = new Error(`المسار غير موجود - ${req.originalUrl}`);
    res.status(404); // تعيين حالة الاستجابة إلى 404 (غير موجود)
    next(error); // تمرير الخطأ إلى middleware معالجة الأخطاء التالي
};

/**
 * دالة `errorHandler`.ل
 * middleware مركزي لمعالجة الأخطاء في تطبيق Express.
 * يلتقط الأخطاء التي يتم تمريرها بواسطة `next(error)` أو التي يتم رميها في معالجات المسارات.
 * 
 * @param {object} err - كائن الخطأ الذي تم التقاطه.
 * @param {object} req - كائن الطلب من Express.
 * @param {object} res - كائن الاستجابة من Express.
 * @param {function} next - دالة رد النداء (غير مستخدمة هنا بشكل مباشر، ولكن ضرورية لتوقيع middleware الأخطاء).
 * @returns {void} - ترسل استجابة JSON للعميل مع تفاصيل الخطأ.
 */
const errorHandler = (err, req, res, next) => {
    // تحديد رمز حالة HTTP للاستجابة. إذا كانت الحالة الأصلية 200 (نجاح)، يتم تغييرها إلى 500 (خطأ خادم).
    let statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    let message = err.message; // رسالة الخطأ

    // معالجة خاصة لأخطاء Mongoose CastError (تحدث عندما يكون معرف الكائن غير صالح)
    if (err.name === 'CastError' && err.kind === 'ObjectId') {
        statusCode = 404; // تغيير الحالة إلى 404 (غير موجود)
        message = 'المورد غير موجود (صيغة المعرف غير صالحة)'; // رسالة خطأ أكثر وضوحًا للمستخدم
    }

    // تحديد ما إذا كان يجب التقاط الخطأ وإرساله إلى Sentry
    // يتم التقاط أخطاء الخادم (500+) وأخطاء العميل (400+) غير المتوقعة.
    const shouldCaptureToSentry = statusCode >= 500 ||
        (statusCode >= 400 && err.name !== 'ValidationError' && // استبعاد أخطاء التحقق من الصحة
         !message.includes('تم تسليم هذا الاختبار مسبقًا') && // استبعاد رسائل خطأ محددة
         !message.includes('لا يمكن تعديل الإجابات بعد إكمال الاختبار') &&
         !message.includes('المسار غير موجود') && // استبعاد أخطاء 404 التي يتم التعامل معها بواسطة `notFound`
         !req.originalUrl.includes('favicon.ico')); // استبعاد طلبات أيقونة المفضلة

    if (shouldCaptureToSentry) {
        // التقاط الخطأ وإرساله إلى Sentry مع سياق مفصل
        captureErrorWithContext(err, {
            context: {
                component: 'errorMiddleware',
                operation: 'errorHandler',
                statusCode,
                errorName: err.name,
                errorCode: err.code,
            },
            user: req.user || null, // تمرير بيانات المستخدم إذا كانت متاحة
            request: req, // تمرير كائن الطلب الكامل
        });
    }

    // تسجيل الخطأ في ملفات السجل باستخدام winston
    logger.error(message, {
        stack: err.stack, // تتبع المكدس (stack trace)
        url: req.originalUrl, // عنوان URL الأصلي للطلب
        method: req.method, // طريقة الطلب (GET, POST, إلخ)
        ip: req.ip, // عنوان IP للعميل
        userId: req.user?._id, // معرف المستخدم إذا كان مصادقًا عليه
        statusCode, // رمز حالة HTTP
    });

    // إرسال استجابة JSON للعميل
    res.status(statusCode).json({
        message: message, // رسالة الخطأ
        // عرض تتبع المكدس فقط في وضع التطوير لأغراض التصحيح والأمان
        stack: process.env.NODE_ENV === 'production' ? null : err.stack,
    });
};

// تصدير دوال middleware
module.exports = { notFound, errorHandler };
