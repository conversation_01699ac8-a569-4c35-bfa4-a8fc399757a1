# الواجهة الخلفية للتطبيق (Backend Application)

يمثل هذا المجلد الواجهة الخلفية لتطبيق "Tumrah Testing Platform"، وهو مبني باستخدام Node.js و Express.js. توفر هذه الواجهة الخلفية واجهة برمجة التطبيقات (API) التي تخدم الواجهة الأمامية، وتدير منطق الأعمال، وتتفاعل مع قاعدة بيانات MongoDB.

## هيكل المجلد

يتكون هذا المجلد من عدة مجلدات فرعية رئيسية وملفات، كل منها مسؤول عن جانب معين من جوانب الواجهة الخلفية:

*   **`config/`**: يحتوي على ملفات الإعدادات والتكوينات للخدمات المختلفة مثل التسجيل (logging) ومراقبة الأخطاء (Sentry).
*   **`controllers/`**: يحتوي على دوال التحكم (controller functions) التي تعالج منطق الأعمال الخاص بطلبات API الواردة.
*   **`middleware/`**: يحتوي على دوال وسيطة (middleware functions) لمعالجة الطلبات والاستجابات، مثل المصادقة، معالجة الأخطاء، والتحقق من صحة البيانات.
*   **`models/`**: يحتوي على تعريفات نماذج البيانات (Mongoose Schemas and Models) التي تمثل بنية المستندات في قاعدة بيانات MongoDB.
*   **`routes/`**: يحدد مسارات API المختلفة للتطبيق ويوجهها إلى دوال التحكم المناسبة.
*   **`tests/`**: يحتوي على الاختبارات الآلية للواجهة الخلفية.
*   **`validators/`**: يحتوي على دوال التحقق من صحة البيانات الواردة في الطلبات.
*   **`app.js`**: الملف الرئيسي الذي يقوم بإعداد تطبيق Express، وتكوين middleware، وربط المسارات.
*   **`server.js`**: نقطة الدخول لتشغيل الخادم، ويتعامل مع الاتصال بقاعدة بيانات MongoDB.
*   **`swaggerConfig.js`**: ملف إعدادات Swagger/OpenAPI لإنشاء وثائق API تفاعلية.
*   **ملفات أخرى**: مثل `package.json` (تعريف المشروع والتبعيات)، `.env` (متغيرات البيئة)، وملفات السجل (`combined.log`, `error.log`).

## التقنيات المستخدمة

*   **Node.js**: بيئة تشغيل JavaScript من جانب الخادم.
*   **Express.js**: إطار عمل ويب سريع وغير مقيد لـ Node.js.
*   **MongoDB**: قاعدة بيانات NoSQL.
*   **Mongoose**: مكتبة لنمذجة كائنات MongoDB لـ Node.js.
*   **JWT (JSON Web Tokens)**: للمصادقة القائمة على التوكن.
*   **Winston**: لمكتبة التسجيل (logging).
*   **Sentry**: لمراقبة الأخطاء والأداء.
*   **Swagger/OpenAPI**: لتوثيق API.
*   **Jest**: لإطار عمل الاختبار.
*   **Supertest**: لاختبار HTTP.
*   **express-validator**: للتحقق من صحة البيانات.
*   **express-rate-limit**: لتحديد معدل الطلبات.

## كيفية التشغيل

للتشغيل المحلي للواجهة الخلفية، تأكد من تثبيت Node.js و npm (أو yarn).

1.  **انتقل إلى مجلد الواجهة الخلفية:**
    ```bash
    cd backend
    ```
2.  **تثبيت التبعيات:**
    ```bash
    npm install
    # أو
    yarn install
    ```
3.  **إعداد متغيرات البيئة:**
    قم بإنشاء ملف `.env` في المجلد الجذر للواجهة الخلفية (`backend/`) واملأه بالمتغيرات الضرورية مثل:
    ```
    PORT=5000
    MONGODB_URI=mongodb://localhost:27017/tumrah_db # أو URI قاعدة بيانات MongoDB Atlas
    JWT_SECRET=your_jwt_secret_key
    TELEGRAM_BOT_TOKEN=your_telegram_bot_token
    SENTRY_DSN=your_sentry_dsn # اختياري
    FRONTEND_URL=http://localhost:5173 # عنوان URL للواجهة الأمامية
    ```
4.  **تشغيل الخادم:**
    ```bash
    npm start
    # أو
    node server.js
    ```
    سيتم تشغيل الخادم عادةً على `http://localhost:5000`.

## الوظائف الرئيسية

*   **مصادقة المستخدم**: تسجيل الدخول عبر Telegram.
*   **إدارة المستخدمين**: جلب وتحديث ملفات المستخدم الشخصية، وجلب إحصائيات الأداء.
*   **إدارة الأسئلة**: عمليات CRUD للأسئلة بأنواعها المختلفة.
*   **إدارة جلسات الاختبار**: بدء الاختبارات، حفظ الإجابات، تسليم الاختبارات، وحساب النتائج.
*   **معالجة الأخطاء**: نظام شامل لتسجيل الأخطاء ومراقبتها.
*   **توثيق API**: وثائق تفاعلية لجميع نقاط نهاية API.
