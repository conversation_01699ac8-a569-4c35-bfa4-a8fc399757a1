// استيراد هوكس React لإدارة الحالة ودورة حياة المكونات
import { useState, useEffect } from 'react';
// استيراد هوك useNavigate للتنقل البرمجي بين المسارات
import { useNavigate } from 'react-router-dom';
// استيراد هوك useAuth للوصول إلى بيانات المستخدم وحالة المصادقة
import { useAuth } from '../context/AuthContext';
// استيراد دوال API لإجراء طلبات مصادق عليها (GET, DELETE)
import { authenticatedGet, authenticatedRequest } from '../utils/api';
// استيراد دالة Sentry لالتقاط الأخطاء مع السياق
import { captureErrorWithContext } from '../config/sentry';

/**
 * مكون AdminQuestionsPage (صفحة إدارة الأسئلة للمدير).
 * يسمح هذا المكون للمستخدمين ذوي صلاحيات المدير (admin/sudo) بعرض،
 * إضافة، تعديل، وحذف الأسئلة في قاعدة البيانات.
 * 
 * @returns {JSX.Element} - صفحة إدارة الأسئلة.
 */
const AdminQuestionsPage = () => {
    // حالة لتخزين قائمة الأسئلة
    const [questions, setQuestions] = useState([]);
    // حالة التحميل: true أثناء جلب البيانات
    const [loading, setLoading] = useState(true);
    // حالة الخطأ: لتخزين رسائل الخطأ
    const [error, setError] = useState('');
    // حالة التحميل للحذف: لتتبع السؤال الذي يتم حذفه حاليًا
    const [deleteLoading, setDeleteLoading] = useState(null);
    // حالة رسالة النجاح: لعرض رسائل النجاح المؤقتة
    const [successMessage, setSuccessMessage] = useState('');

    // الحصول على بيانات المستخدم من سياق المصادقة
    const { user } = useAuth();
    // هوك للتنقل بين المسارات
    const navigate = useNavigate();

    // تأثير جانبي (useEffect) للتحقق من صلاحيات المستخدم عند تحميل المكون أو تغير المستخدم
    useEffect(() => {
        // إذا كان المستخدم موجودًا وليس لديه صلاحيات 'admin' أو 'sudo'، يتم إعادة توجيهه
        if (user && !['admin', 'sudo'].includes(user.role)) {
            navigate('/dashboard');
            return; // إيقاف التنفيذ
        }
    }, [user, navigate]); // يعتمد على `user` و `navigate`

    // تأثير جانبي (useEffect) لجلب الأسئلة من الخادم
    useEffect(() => {
        const fetchQuestions = async () => {
            // التحقق من وجود توكن المستخدم قبل جلب الأسئلة
            if (!user?.token) {
                setError('المصادقة مطلوبة');
                setLoading(false);
                return;
            }

            try {
                setLoading(true); // بدء التحميل
                setError(''); // مسح أي أخطاء سابقة
                // إرسال طلب GET مصادق عليه لجلب جميع الأسئلة
                const data = await authenticatedGet(
                    '/api/questions',
                    user.token,
                    {},
                    {
                        component: 'AdminQuestionsPage',
                        operation: 'fetchQuestions'
                    }
                );
                setQuestions(data); // تحديث حالة الأسئلة بالبيانات المستلمة
            } catch (err) {
                // معالجة الأخطاء بناءً على نوعها
                if (err.status === 403) {
                    setError('الوصول مرفوض. صلاحيات المدير مطلوبة.');
                } else {
                    setError(err.message || 'فشل جلب الأسئلة');
                }

                // التقاط الخطأ وإرساله إلى Sentry مع السياق
                captureErrorWithContext(err, {
                    context: {
                        component: 'AdminQuestionsPage',
                        operation: 'fetchQuestions',
                        userId: user?._id,
                    },
                });
            } finally {
                setLoading(false); // إنهاء التحميل بغض النظر عن النتيجة
            }
        };

        // استدعاء دالة جلب الأسئلة
        fetchQuestions();
    }, [user]); // يعتمد على `user` لإعادة الجلب عند تغيره

    /**
     * دالة `handleDelete`.
     * تقوم بحذف سؤال محدد من قاعدة البيانات.
     * 
     * @param {string} questionId - معرف السؤال المراد حذفه.
     */
    const handleDelete = async (questionId) => {
        // البحث عن السؤال المراد حذفه لعرض نصه في رسالة التأكيد
        const question = questions.find(q => q._id === questionId);
        const questionText = question?.question_text ||
                           (question?.passage ? `مقطع قراءة (${question.passage.substring(0, 30)}...)` : 'سؤال غير معروف');

        // طلب تأكيد من المستخدم قبل الحذف
        if (!window.confirm(`هل أنت متأكد أنك تريد حذف هذا السؤال؟\n\n"${questionText}"\n\nلا يمكن التراجع عن هذا الإجراء.`)) {
            return; // إلغاء الحذف إذا لم يؤكد المستخدم
        }

        try {
            setDeleteLoading(questionId); // تعيين حالة التحميل لزر الحذف المحدد
            setError(''); // مسح أي أخطاء سابقة

            // إرسال طلب DELETE مصادق عليه لحذف السؤال
            await authenticatedRequest(
                `/api/questions/${questionId}`,
                user.token,
                { method: 'DELETE' },
                {
                    component: 'AdminQuestionsPage',
                    operation: 'deleteQuestion'
                }
            );

            // تحديث قائمة الأسئلة في الواجهة الأمامية بعد الحذف
            setQuestions(prevQuestions => prevQuestions.filter(q => q._id !== questionId));

            // عرض رسالة نجاح مؤقتة
            setSuccessMessage('تم حذف السؤال بنجاح');
            setTimeout(() => setSuccessMessage(''), 3000);

        } catch (err) {
            setError(err.message || 'فشل حذف السؤال'); // عرض رسالة الخطأ
            setTimeout(() => setError(''), 5000);

            // التقاط الخطأ وإرساله إلى Sentry مع السياق
            captureErrorWithContext(err, {
                context: {
                    component: 'AdminQuestionsPage',
                    operation: 'deleteQuestion',
                    questionId,
                    userId: user?._id,
                },
            });
        } finally {
            setDeleteLoading(null); // إزالة حالة التحميل من زر الحذف
        }
    };

    /**
     * دالة مساعدة `getQuestionDisplayText`.ل
     * تقوم بإنشاء نص عرض مختصر للسؤال في الجدول.
     * 
     * @param {object} question - كائن السؤال.
     * @returns {string} - نص عرض السؤال.
     */
    const getQuestionDisplayText = (question) => {
        if (question.question_text) {
            return question.question_text.length > 100
                ? `${question.question_text.substring(0, 100)}...` 
                : question.question_text;
        }
        if (question.passage) {
            return `فهم مقروء (${question.passage.substring(0, 50)}...)`;
        }
        return 'لا يوجد نص سؤال متاح';
    };

    /**
     * دالة مساعدة `formatQuestionType`.
     * تقوم بتحويل نوع السؤال من مفتاح (key) إلى نص قابل للقراءة.
     * 
     * @param {string} type - نوع السؤال (مثال: 'completion').
     * @returns {string} - نوع السؤال المنسق (مثال: 'Completion').
     */
    const formatQuestionType = (type) => {
        const typeMap = {
            'completion': 'إكمال',
            'error': 'اكتشاف الخطأ',
            'analogy': 'تشبيه',
            'reading': 'فهم مقروء',
            'odd_one_out': 'الشاذ'
        };
        return typeMap[type] || type; // إرجاع النوع المنسق أو النوع الأصلي إذا لم يوجد في الخريطة
    };

    // حالة التحميل: عرض مؤشر تحميل أثناء جلب الأسئلة
    if (loading) {
        return (
            <div className="dashboard-container">
                <div className="loading">
                    <div className="spinner"></div>
                    <span>جاري تحميل الأسئلة...</span>
                </div>
            </div>
        );
    }

    // حالة رفض الوصول: عرض رسالة إذا لم يكن المستخدم لديه الصلاحيات الكافية
    // (هذا التحقق يتم أيضًا في useEffect، ولكن هذا يوفر واجهة مستخدم مخصصة)
    if (user && !['admin', 'sudo'].includes(user.role)) {
        return (
            <div className="dashboard-container">
                <div className="card text-center">
                    <h3 className="text-error mb-md">الوصول مرفوض</h3>
                    <p className="mb-lg">ليس لديك إذن للوصول إلى هذه الصفحة. صلاحيات المدير مطلوبة.</p>
                    <button onClick={() => navigate('/dashboard')} className="btn btn-primary">
                        العودة إلى لوحة التحكم
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="main-content">
            {/* قسم العنوان وزر إضافة سؤال جديد */}
            <div className="text-center mb-xl">
                <h1 className="mb-sm">إدارة الأسئلة</h1>
                <p className="text-secondary mb-lg">إدارة أسئلة الاختبار والمحتوى</p>
                <button
                    onClick={() => navigate('/admin/questions/new')}
                    className="btn btn-primary"
                >
                    إضافة سؤال جديد
                </button>
            </div>

            {/* رسالة النجاح (تظهر مؤقتًا) */}
            {successMessage && (
                <div className="success-message mb-lg">
                    {successMessage}
                </div>
            )}

            {/* رسالة الخطأ (تظهر مؤقتًا) */}
            {error && (
                <div className="error-message mb-lg">
                    <strong>خطأ:</strong> {error}
                </div>
            )}

            {/* قسم عرض الأسئلة */}
            <div className="mb-lg">
                <h3 className="mb-md">الأسئلة ({questions.length})</h3>
                {questions.length > 0 && (
                    <p className="text-secondary mb-lg">
                        عرض جميع الأسئلة في قاعدة البيانات
                    </p>
                )}
            </div>

            {/* عرض رسالة "لا توجد أسئلة" إذا كانت القائمة فارغة */}
            {questions.length === 0 ? (
                <div className="card text-center">
                    <div className="mb-lg" style={{ fontSize: '3rem', opacity: 0.5 }}>📝</div>
                    <h3 className="mb-md">لم يتم العثور على أسئلة</h3>
                    <p className="text-secondary mb-lg">
                        لا توجد أسئلة في قاعدة البيانات حتى الآن. أضف سؤالك الأول للبدء.
                    </p>
                    <button
                        onClick={() => navigate('/admin/questions/new')}
                        className="btn btn-primary"
                    >
                        إضافة أول سؤال
                    </button>
                </div>
            ) : (
                // عرض جدول الأسئلة إذا كانت القائمة تحتوي على أسئلة
                <div className="card">
                    <table>
                        <thead>
                            <tr>
                                <th>السؤال</th>
                                <th>النوع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {questions.map((question) => (
                                <tr key={question._id}>
                                    <td>
                                        <div>
                                            {getQuestionDisplayText(question)}
                                            {question.type === 'reading' && (
                                                <div className="text-secondary mt-sm">
                                                    {question.questions?.length || 0} أسئلة فرعية
                                                </div>
                                            )}
                                        </div>
                                    </td>
                                    <td>
                                        <span className="text-primary">
                                            {formatQuestionType(question.type)}
                                        </span>
                                    </td>
                                    <td>
                                        {/* زر التعديل */}
                                        <button
                                            onClick={() => navigate(`/admin/questions/edit/${question._id}`)}
                                            className="btn btn-secondary mr-sm"
                                        >
                                            تعديل
                                        </button>
                                        {/* زر الحذف */}
                                        <button
                                            className="btn btn-danger"
                                            onClick={() => handleDelete(question._id)}
                                            disabled={deleteLoading === question._id}
                                        >
                                            {deleteLoading === question._id ? (
                                                <div className="loading">
                                                    <div className="spinner"></div>
                                                    جاري الحذف...
                                                </div>
                                            ) : (
                                                'حذف'
                                            )}
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

// تصدير المكون ليكون متاحًا للاستخدام في أجزاء أخرى من التطبيق
export default AdminQuestionsPage;
