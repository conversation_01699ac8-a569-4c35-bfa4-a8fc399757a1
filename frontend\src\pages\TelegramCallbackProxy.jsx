// استيراد هوك useEffect لإدارة دورة حياة المكون
import { useEffect } from 'react';
// استيراد هوك useLocation للوصول إلى معلومات URL الحالية
import { useLocation } from 'react-router-dom';

/**
 * مكون TelegramCallbackProxy (وكيل رد نداء تليجرام).
 * يعمل هذا المكون كوكيل لمعالجة رد نداء المصادقة من تليجرام.
 * يقوم بالتقاط معلمات الاستجابة من عنوان URL الحالي وإعادة توجيهها إلى الواجهة الخلفية
 * لإكمال عملية المصادقة.
 * 
 * @returns {JSX.Element} - واجهة مستخدم بسيطة تشير إلى عملية إعادة التوجيه.
 */
const TelegramCallbackProxy = () => {
  // هوك للوصول إلى كائن الموقع (location object) الذي يحتوي على معلومات URL الحالية
  const location = useLocation();

  // تأثير جانبي (useEffect) يتم تشغيله عند تحميل المكون أو تغير معلمات البحث في URL
  useEffect(() => {
    // الحصول على جميع معلمات البحث (query parameters) من عنوان URL الحالي
    const queryParams = location.search;

    // بناء عنوان URL للواجهة الخلفية لإعادة توجيه الطلب
    // يتم استخدام VITE_API_BASE_URL من متغيرات البيئة، أو http://localhost:5000 كقيمة افتراضية
    const backendBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
    const backendUrl = `${backendBaseUrl}/api/users/auth/telegram/callback${queryParams}`;

    // تسجيل عنوان URL للواجهة الخلفية لأغراض التصحيح
    console.log('جاري إعادة التوجيه إلى الواجهة الخلفية لمصادقة تليجرام:', backendUrl);

    // إعادة توجيه المتصفح بالكامل إلى عنوان URL للواجهة الخلفية.
    // ستقوم الواجهة الخلفية بمعالجة المصادقة ثم إعادة التوجيه مرة أخرى إلى الواجهة الأمامية.
    window.location.href = backendUrl;
  }, [location.search]); // يعتمد على `location.search` لإعادة تشغيل التأثير عند تغير معلمات البحث

  return (
    // واجهة مستخدم بسيطة تظهر أثناء عملية إعادة التوجيه
    <div className="dashboard-container">
      <div className="card text-center">
        <div className="mb-lg" style={{ fontSize: '3rem' }}>🔄</div>
        <h2 className="mb-md">جاري معالجة مصادقة تليجرام...</h2>
        <p className="text-secondary mb-lg">
          الرجاء الانتظار بينما نقوم بإعادة توجيهك لإكمال عملية المصادقة.
        </p>
        <div className="loading">
          <div className="spinner"></div>
          <span>جاري إعادة التوجيه...</span>
        </div>
      </div>
    </div>
  );
};

// تصدير المكون ليكون متاحًا للاستخدام في مسارات التطبيق
export default TelegramCallbackProxy;