# إعدادات الواجهة الخلفية (Backend Configuration)

يحتوي هذا المجلد على ملفات الإعدادات والتكوينات الخاصة بالواجهة الخلفية للتطبيق. هذه الملفات ضرورية لتهيئة الخدمات الأساسية مثل التسجيل (logging) ومراقبة الأخطاء (error monitoring).

## هيكل المجلد

يتكون المجلد من ملفات JavaScript، حيث يختص كل ملف بإعداد جانب معين من جوانب الواجهة الخلفية.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `logger.js`
يقوم هذا الملف بتهيئة نظام التسجيل (logging system) باستخدام مكتبة `winston`. يحدد مستويات التسجيل المخصصة، الألوان، ويقوم بإعداد نواقل (transports) مختلفة للسجلات، بما في ذلك التسجيل في ملفات منفصلة للأخطاء والسجلات المجمعة، بالإضافة إلى التسجيل في الطرفية (console) في وضع التطوير.

### `sentry.js`
يقوم هذا الملف بتهيئة مكتبة Sentry لمراقبة الأخطاء والأداء والتنميط في الواجهة الخلفية. يتضمن إعدادات DSN، البيئة، معدلات أخذ العينات للتتبع والتنميط، بالإضافة إلى تكاملات Sentry مع Express.js و MongoDB. كما يحتوي على منطق تصفية متقدم للأخطاء وتنظيف البيانات الحساسة قبل إرسالها إلى Sentry، ويوفر دوال مساعدة لالتقاط الأخطاء والرسائل وتعيين سياق المستخدم.
