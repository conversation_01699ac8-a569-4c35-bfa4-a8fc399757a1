// backend/controllers/userController.js

// استيراد `express-async-handler` لتبسيط معالجة الأخطاء في الدوال غير المتزامنة
const asyncHandler = require('express-async-handler');
// استيراد مكتبة `crypto` لتنفيذ عمليات التشفير (مثل التحقق من تجزئة تليجرام)
const crypto = require('crypto');
// استيراد نماذج Mongoose
const User = require('../models/User'); // نموذج المستخدم
const TestSession = require('../models/TestSession'); // نموذج جلسة الاختبار (لإحصائيات المستخدم)
// استيراد مكتبة `jsonwebtoken` لإنشاء والتحقق من توكنات JWT
const jwt = require('jsonwebtoken');
// استيراد دالة Sentry لالتقاط الأخطاء مع السياق
const { captureErrorWithContext } = require('../config/sentry');

/**
 * دالة `generateToken`.
 * تنشئ توكن JWT (JSON Web Token) للمستخدم المحدد.
 * 
 * @param {string} id - معرف المستخدم (عادةً `_id` من قاعدة البيانات).
 * @returns {string} - توكن JWT الموقع.
 */
const generateToken = (id) => {
    // توقيع التوكن باستخدام معرف المستخدم، المفتاح السري من متغيرات البيئة، وتعيين صلاحية ليوم واحد
    return jwt.sign({ id }, process.env.JWT_SECRET, { expiresIn: '1d' });
};

/**
 * دالة `authWithTelegram`.
 * تتحكم في عملية المصادقة باستخدام بيانات المستخدم القادمة من Telegram.
 * تقوم بالتحقق من صحة البيانات، ثم تبحث عن المستخدم أو تنشئه، وتصدر توكن JWT.
 * 
 * @route POST /api/users/auth/telegram
 * @access عام (Public)
 * @param {object} req - كائن الطلب (يحتوي على بيانات Telegram في `req.body`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل بيانات المستخدم والتوكن.
 * @throws {Error} - ترمي خطأ إذا كانت البيانات غير صالحة أو حدث خطأ في الخادم.
 */
const authWithTelegram = asyncHandler(async (req, res) => {
    const telegramUser = req.body; // بيانات المستخدم القادمة من Telegram
    // تجاوز التحقق من التجزئة في وضع التطوير باستخدام تجزئة اختبارية
    const isDevelopmentTestLogin = process.env.NODE_ENV === 'development' && telegramUser.hash === 'test_hash_for_development';

    if (!isDevelopmentTestLogin) {
        try {
            // 1. التحقق من صحة بيانات Telegram باستخدام HMAC-SHA256
            // المفتاح السري هو تجزئة SHA256 لتوكن بوت Telegram
            const secretKey = crypto.createHash('sha256').update(process.env.TELEGRAM_BOT_TOKEN).digest();
            // بناء سلسلة التحقق من جميع حقول بيانات المستخدم باستثناء 'hash'
            const checkString = Object.keys(telegramUser)
                .filter(key => key !== 'hash') // استبعاد حقل التجزئة
                .map(key => (`${key}=${telegramUser[key]}`)) // تحويل كل زوج مفتاح-قيمة إلى سلسلة
                .sort() // ترتيب السلاسل أبجديًا
                .join('\n'); // دمج السلاسل بفاصل سطر جديد

            // حساب تجزئة HMAC باستخدام المفتاح السري وسلسلة التحقق
            const hmac = crypto.createHmac('sha256', secretKey).update(checkString).digest('hex');

            // مقارنة التجزئة المحسوبة بالتجزئة المستلمة من Telegram
            if (hmac !== telegramUser.hash) {
                return res.status(401).json({ message: 'بيانات غير صالحة: فشل التحقق من المصدر.' });
            }
        } catch (error) {
            console.error("خطأ في التحقق من تجزئة تليجرام:", error);
            // التقاط الخطأ وإرساله إلى Sentry
            captureErrorWithContext(error, {
                context: {
                    component: 'userController',
                    operation: 'authWithTelegram_hashVerification',
                    telegramUserId: telegramUser.id,
                },
                request: req, // تمرير كائن الطلب إلى Sentry
            });
            return res.status(500).json({ message: 'خطأ أثناء التحقق من بيانات Telegram.' });
        }
    }

    try {
        // 2. البحث عن المستخدم أو إنشاؤه
        let user = await User.findOne({ telegramId: telegramUser.id });

        if (!user) {
            // إذا لم يتم العثور على المستخدم، يتم إنشاء مستخدم جديد
            user = await User.create({
                telegramId: telegramUser.id,
                fullName: `${telegramUser.first_name} ${telegramUser.last_name || ''}`.trim(),
                username: telegramUser.username,
            });
        }

        // 3. التحقق مما إذا كان الحساب محظورًا
        if (user.role === 'banned') {
            return res.status(403).json({ message: 'هذا الحساب محظور.' });
        }

        // 4. إنشاء توكن JWT للمستخدم
        const token = generateToken(user._id);

        // 5. إرسال بيانات المستخدم والتوكن إلى الواجهة الأمامية
        res.json({
            _id: user._id,
            fullName: user.fullName,
            email: user.email,
            role: user.role,
            level: user.level,
            token: token,
            message: 'تم تسجيل الدخول بنجاح.'
        });
    } catch (error) {
        console.error('خطأ أثناء المصادقة عبر Telegram:', error);
        // التقاط الخطأ وإرساله إلى Sentry
        captureErrorWithContext(error, {
            context: {
                component: 'userController',
                operation: 'authWithTelegram_userCreation',
                telegramUserId: telegramUser.id,
            },
            request: req,
        });
        res.status(500).json({ message: 'خطأ في الخادم أثناء المصادقة.' });
    }
});

/**
 * دالة `telegramAuthCallback`.ل
 * معالج إعادة التوجيه من Telegram OAuth (رد نداء من جانب الخادم).
 * تقوم بالتحقق من صحة البيانات، ثم تبحث عن المستخدم أو تنشئه، وتصدر توكن JWT،
 * ثم تعيد توجيه المتصفح إلى الواجهة الأمامية مع التوكن أو رسالة خطأ.
 * 
 * @route GET /api/users/auth/telegram/callback
 * @access عام (Public)
 * @param {object} req - كائن الطلب (يحتوي على بيانات Telegram في `req.query`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - تقوم بإعادة توجيه المتصفح.
 */
const telegramAuthCallback = asyncHandler(async (req, res) => {
    const telegramUser = req.query; // بيانات المستخدم القادمة من Telegram (في معلمات الاستعلام)
    // تجاوز التحقق من التجزئة في وضع التطوير
    const isDevelopmentTestLogin = process.env.NODE_ENV === 'development' && telegramUser.hash === 'test_hash_for_development';

    if (!isDevelopmentTestLogin) {
        try {
            // التحقق من صحة بيانات Telegram (نفس منطق authWithTelegram)
            const secretKey = crypto.createHash('sha256').update(process.env.TELEGRAM_BOT_TOKEN).digest();
            const checkString = Object.keys(telegramUser)
                .filter(key => key !== 'hash')
                .map(key => (`${key}=${telegramUser[key]}`))
                .sort()
                .join('\n');
            const hmac = crypto.createHmac('sha256', secretKey).update(checkString).digest('hex');

            if (hmac !== telegramUser.hash) {
                // إعادة التوجيه إلى الواجهة الأمامية مع خطأ إذا فشل التحقق
                return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/callback?error=invalid_hash`);
            }
        } catch (error) {
            console.error("خطأ في التحقق من تجزئة تليجرام:", error);
            return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/callback?error=verification_failed`);
        }
    }

    try {
        // البحث عن المستخدم أو إنشاؤه
        let user = await User.findOne({ telegramId: telegramUser.id });

        if (!user) {
            user = await User.create({
                telegramId: telegramUser.id,
                fullName: `${telegramUser.first_name} ${telegramUser.last_name || ''}`.trim(),
                username: telegramUser.username,
            });
        }

        // التحقق مما إذا كان الحساب محظورًا
        if (user.role === 'banned') {
            return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/callback?error=banned`);
        }

        // إنشاء توكن JWT للمستخدم
        const token = generateToken(user._id);
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        // إعادة التوجيه إلى الواجهة الأمامية مع التوكن
        res.redirect(`${frontendUrl}/auth/callback?token=${token}`);
    } catch (error) {
        console.error('خطأ أثناء المصادقة عبر Telegram callback:', error);
        return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/callback?error=server_error`);
    }
});

/**
 * دالة `getUserProfile`.ل
 * تتحكم في جلب ملف المستخدم الشخصي للمستخدم المصادق عليه.
 * 
 * @route GET /api/users/profile
 * @access خاص (Private) - يتطلب مصادقة.
 * @param {object} req - كائن الطلب (يحتوي على بيانات المستخدم في `req.user` بعد المصادقة).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل بيانات ملف المستخدم الشخصي.
 */
const getUserProfile = asyncHandler(async (req, res) => {
    const user = req.user; // بيانات المستخدم المرفقة بكائن الطلب بواسطة middleware المصادقة
    if (user) {
        res.json({
            _id: user._id,
            email: user.email,
            fullName: user.fullName,
            role: user.role,
            level: user.level,
            preferences: user.preferences,
            createdAt: user.createdAt
        });
    } else {
        res.status(404).json({ message: 'المستخدم غير موجود' });
    }
});

/**
 * دالة `updateUserProfile`.ل
 * تتحكم في تحديث ملف المستخدم الشخصي للمستخدم المصادق عليه.
 * 
 * @route PUT /api/users/profile
 * @access خاص (Private) - يتطلب مصادقة.
 * @param {object} req - كائن الطلب (يحتوي على بيانات التحديث في `req.body` وبيانات المستخدم في `req.user`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل بيانات المستخدم المحدثة وتوكن جديد.
 */
const updateUserProfile = asyncHandler(async (req, res) => {
    // البحث عن المستخدم في قاعدة البيانات
    const user = await User.findById(req.user._id);
    if (user) {
        // تحديث الحقول إذا تم توفيرها في جسم الطلب
        user.fullName = req.body.fullName || user.fullName;
        user.email = req.body.email || user.email;
        if (req.body.preferences) {
            user.preferences.language = req.body.preferences.language || user.preferences.language;
        }
        try {
            const updatedUser = await user.save(); // حفظ التغييرات في قاعدة البيانات
            // إرسال بيانات المستخدم المحدثة وتوكن جديد (لأن البيانات قد تكون تغيرت)
            res.json({
                _id: updatedUser._id,
                email: updatedUser.email,
                fullName: updatedUser.fullName,
                role: updatedUser.role,
                level: updatedUser.level,
                preferences: updatedUser.preferences,
                token: generateToken(updatedUser._id) // إصدار توكن جديد
            });
        } catch (error) {
             // معالجة خطأ تكرار البريد الإلكتروني (رمز 11000 من MongoDB)
             if (error.code === 11000) {
                 return res.status(400).json({ message: 'هذا البريد الإلكتروني مستخدم بالفعل.' });
            }
            res.status(500).json({ message: 'خطأ في الخادم أثناء تحديث الملف الشخصي' });
        }
    } else {
        res.status(404).json({ message: 'المستخدم غير موجود' });
    }
});

/**
 * دالة `getUserStats`.ل
 * تتحكم في جلب إحصائيات المستخدم (عدد الاختبارات المكتملة، متوسط الدرجة، أفضل درجة).
 * 
 * @route GET /api/users/stats
 * @access خاص (Private) - يتطلب مصادقة.
 * @param {object} req - كائن الطلب (يحتوي على معرف المستخدم في `req.user._id`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل كائن الإحصائيات.
 */
const getUserStats = asyncHandler(async (req, res) => {
  // جلب جميع جلسات الاختبار المكتملة للمستخدم الحالي
  const completedTests = await TestSession.find({
    user: req.user._id,
    status: 'completed',
  });

  // إذا لم يكن هناك اختبارات مكتملة، أعد إحصائيات صفرية
  if (!completedTests || completedTests.length === 0) {
    return res.json({
      totalTests: 0,
      averageScore: 0,
      bestScore: 0,
    });
  }

  const totalTests = completedTests.length; // العدد الكلي للاختبارات المكتملة
  // حساب مجموع الدرجات لجميع الاختبارات المكتملة
  const totalScore = completedTests.reduce((acc, test) => acc + (test.score || 0), 0);
  // حساب متوسط الدرجة وتقريبه
  const averageScore = Math.round(totalScore / totalTests);
  // إيجاد أفضل درجة (أعلى درجة)
  const bestScore = Math.max(...completedTests.map(test => test.score || 0));

  // إرسال الإحصائيات
  res.json({
    totalTests,
    averageScore,
    bestScore,
  });
});

// تصدير جميع دوال التحكم في المستخدم
module.exports = {
    authWithTelegram,
    telegramAuthCallback,
    getUserProfile,
    updateUserProfile,
    getUserStats,
};
