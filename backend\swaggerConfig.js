// backend/swaggerConfig.js

// استيراد مكتبة `swagger-jsdoc` لإنشاء مواصفات OpenAPI من تعليقات JSDoc
const swaggerJSDoc = require('swagger-jsdoc');

/**
 * `swaggerDefinition` (تعريف Swagger).
 * يحدد المعلومات الأساسية حول واجهة برمجة التطبيقات (API)
 * وفقًا لمواصفات OpenAPI.
 */
const swaggerDefinition = {
  openapi: '3.0.0', // تحديد إصدار OpenAPI
  info: {
    title: 'Tamruh API', // عنوان واجهة برمجة التطبيقات
    version: '1.0.0', // إصدار واجهة برمجة التطبيقات
    description: 'التوثيق الرسمي لواجهة برمجة تطبيقات تطبيق تمرح (Tamruh)', // وصف واجهة برمجة التطبيقات
  },
  servers: [ // تعريف الخوادم التي تستضيف واجهة برمجة التطبيقات
    {
      url: 'http://localhost:5000', // عنوان URL للخادم المحلي
      description: 'خادم التطوير المحلي', // وصف الخادم
    },
  ],
};

/**
 * `options` (خيارات Swagger-JSDoc).
 * تحدد كيفية قيام `swagger-jsdoc` بإنشاء مواصفات OpenAPI.
 */
const options = {
  swaggerDefinition, // استخدام تعريف Swagger الذي تم إنشاؤه أعلاه
  // المسار إلى الملفات التي تحتوي على تعليقات JSDoc التي سيتم تحليلها لإنشاء التوثيق
  apis: ['./routes/*.js'], // سيتم البحث في جميع ملفات .js داخل مجلد 'routes'
};

// إنشاء مواصفات Swagger (OpenAPI) باستخدام الخيارات المحددة
const swaggerSpec = swaggerJSDoc(options);

// تصدير مواصفات Swagger لاستخدامها مع `swagger-ui-express`
module.exports = swaggerSpec;
