// backend/tests/sentry.test.js

// استيراد مكتبة `supertest` لاختبار طلبات HTTP على تطبيق Express
const request = require('supertest');
// استيراد تطبيق Express الرئيسي
const app = require('../app');
// استيراد دوال Sentry المساعدة من ملف الإعدادات
const { captureErrorWithContext, captureMessageWithContext } = require('../config/sentry');

/**
 * مجموعة اختبارات `Sentry Integration Tests`.
 * تختبر تكامل Sentry في الواجهة الخلفية، بما في ذلك التقاط الأخطاء والرسائل،
 * وتكامل middleware معالجة الأخطاء، ومعالجة أخطاء API.
 */
describe('اختبارات تكامل Sentry', () => {
  /**
   * مجموعة اختبارات `Error Capture`.
   * تختبر ما إذا كانت دوال Sentry لالتقاط الأخطاء والرسائل تعمل بشكل صحيح.
   */
  describe('التقاط الأخطاء', () => {
    /**
     * اختبار: يجب أن تلتقط الأخطاء مع السياق دون رمي استثناء.
     * يتحقق من أن دالة `captureErrorWithContext` يمكن استدعاؤها بأمان.
     */
    test('يجب أن تلتقط الأخطاء مع السياق', () => {
      const testError = new Error('خطأ اختباري لـ Sentry');
      const testContext = {
        component: 'test',
        operation: 'error_capture_test',
        testData: 'بيانات عينة',
      };
      const testUser = {
        _id: 'test-user-id',
        email: '<EMAIL>',
        role: 'user',
      };

      // يجب ألا يرمي هذا الاستدعاء أي خطأ
      expect(() => {
        captureErrorWithContext(testError, {
          context: testContext,
          user: testUser,
        });
      }).not.toThrow();
    });

    /**
     * اختبار: يجب أن تلتقط الرسائل مع السياق دون رمي استثناء.
     * يتحقق من أن دالة `captureMessageWithContext` يمكن استدعاؤها بأمان.
     */
    test('يجب أن تلتقط الرسائل مع السياق', () => {
      const testMessage = 'رسالة اختبار لـ Sentry';
      const testContext = {
        component: 'test',
        operation: 'message_capture_test',
      };

      // يجب ألا يرمي هذا الاستدعاء أي خطأ
      expect(() => {
        captureMessageWithContext(testMessage, 'info', testContext);
      }).not.toThrow();
    });
  });

  /**
   * مجموعة اختبارات `Error Middleware Integration`.
   * تختبر كيفية تعامل middleware معالجة الأخطاء مع أنواع مختلفة من الأخطاء.
   */
  describe('تكامل middleware معالجة الأخطاء', () => {
    /**
     * اختبار: يجب أن يتعامل مع أخطاء 404 (المسار غير موجود) بشكل صحيح.
     * يرسل طلبًا إلى مسار غير موجود ويتوقع استجابة 404 مع رسالة خطأ محددة.
     */
    test('يجب أن يتعامل مع أخطاء 404 بشكل صحيح', async () => {
      const response = await request(app)
        .get('/مسار-غير-موجود') // طلب إلى مسار غير موجود
        .expect(404); // يتوقع حالة 404

      expect(response.body).toHaveProperty('message'); // يتوقع وجود حقل 'message'
      expect(response.body.message).toContain('المسار غير موجود'); // يتوقع رسالة خطأ محددة
    });

    /**
     * اختبار: يجب أن يتعامل مع أخطاء الخادم (500) بشكل صحيح.
     * يرسل طلبًا إلى مسار يسبب خطأ في الخادم ويتوقع استجابة 500.
     * يتم تشغيل هذا الاختبار فقط في بيئة التطوير.
     */
    test('يجب أن يتعامل مع أخطاء الخادم بشكل صحيح', async () => {
      // يتم تشغيل هذا الاختبار فقط إذا كانت بيئة التشغيل هي 'development'
      if (process.env.NODE_ENV === 'development') {
        const response = await request(app)
          .get('/test-error') // مسار يسبب خطأ في الخادم (يجب أن يكون معرفًا في app.js)
          .expect(500); // يتوقع حالة 500

        expect(response.body).toHaveProperty('message');
        expect(response.body.message).toBe('Test error for Sentry integration'); // يتوقع رسالة خطأ محددة
      }
    });
  });

  /**
   * مجموعة اختبارات `API Error Handling`.
   * تختبر كيفية تعامل API مع أخطاء المصادقة والتحقق من الصحة.
   */
  describe('معالجة أخطاء API', () => {
    /**
     * اختبار: يجب أن يتعامل مع أخطاء المصادقة (401) بشكل صحيح.
     * يرسل طلبًا إلى مسار محمي بدون توكن ويتوقع استجابة 401.
     */
    test('يجب أن يتعامل مع أخطاء المصادقة', async () => {
      const response = await request(app)
        .get('/api/users/profile') // مسار يتطلب مصادقة
        .expect(401); // يتوقع حالة 401

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toBe('غير مصرح به، لا يوجد توكن'); // يتوقع رسالة خطأ محددة
    });

    /**
     * اختبار: يجب أن يتعامل مع أخطاء التحقق من الصحة (400) بشكل صحيح.
     * يرسل طلبًا غير صالح إلى مسار يتطلب التحقق من الصحة ويتوقع استجابة 400 مع تفاصيل الأخطاء.
     */
    test('يجب أن يتعامل مع أخطاء التحقق من الصحة', async () => {
      const response = await request(app)
        .post('/api/users/auth/telegram') // مسار يتطلب التحقق من الصحة
        .send({}) // إرسال جسم طلب فارغ ليتسبب في خطأ تحقق
        .expect(400); // يتوقع حالة 400

      expect(response.body).toHaveProperty('errors'); // يتوقع وجود حقل 'errors'
      expect(Array.isArray(response.body.errors)).toBe(true); // يتوقع أن يكون 'errors' مصفوفة
    });
  });

  /**
   * مجموعة اختبارات `Health Check`.
   * تختبر نقطة نهاية فحص الصحة للتطبيق.
   */
  describe('فحص الصحة', () => {
    /**
     * اختبار: يجب أن يعيد حالة صحية.
     * يرسل طلبًا إلى مسار فحص الصحة ويتوقع استجابة 200 وحالة 'UP'.
     */
    test('يجب أن يعيد حالة صحية', async () => {
      const response = await request(app)
        .get('/health') // مسار فحص الصحة
        .expect(200); // يتوقع حالة 200

      expect(response.body).toHaveProperty('status', 'UP'); // يتوقع حقل 'status' بقيمة 'UP'
      expect(response.body).toHaveProperty('timestamp'); // يتوقع وجود حقل 'timestamp'
    });
  });
});

/**
 * مجموعة اختبارات `Sentry Configuration`.
 * تختبر كيفية تعامل Sentry مع سيناريوهات التهيئة المختلفة.
 */
describe('تهيئة Sentry', () => {
  /**
   * اختبار: يجب أن يتعامل مع DSN المفقود بشكل صحيح.
   * يتحقق من أن دوال التقاط الأخطاء لا ترمي استثناءات حتى لو لم يتم تهيئة Sentry.
   */
  test('يجب أن يتعامل مع DSN المفقود بشكل صحيح', () => {
    // إزالة SENTRY_DSN مؤقتًا من متغيرات البيئة
    const originalDsn = process.env.SENTRY_DSN;
    delete process.env.SENTRY_DSN;

    // يجب ألا ترمي هذه الاستدعاءات أي خطأ حتى بدون DSN
    expect(() => {
      captureErrorWithContext(new Error('خطأ اختباري'));
    }).not.toThrow();

    expect(() => {
      captureMessageWithContext('رسالة اختبارية');
    }).not.toThrow();

    // استعادة SENTRY_DSN
    if (originalDsn) {
      process.env.SENTRY_DSN = originalDsn;
    }
  });
});