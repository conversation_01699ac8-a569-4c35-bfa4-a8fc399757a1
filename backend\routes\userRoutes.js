// backend/routes/userRoutes.js

const express = require('express');
const rateLimit = require('express-rate-limit');
const { authWithTelegram, telegramAuthCallback, getUserProfile, updateUserProfile, getUserStats } = require('../controllers/userController');
const { protect } = require('../middleware/authMiddleware');
const { validateTelegramAuth, validateProfileUpdate } = require('../validators/userValidator');
const { handleValidationErrors } = require('../middleware/validationMiddleware');

const router = express.Router();

const authLimiter = rateLimit({
	windowMs: 15 * 60 * 1000,
	max: 20,
	message: { message: 'محاولات مصادقة كثيرة جدًا، يرجى المحاولة مرة أخرى بعد 15 دقيقة' },
    standardHeaders: true,
	legacyHeaders: false,
});

/**
 * @swagger
 * tags:
 *   - name: Users
 *     description: المصادقة وإدارة المستخدمين
 */

/**
 * @swagger
 * /api/users/auth/telegram:
 *   post:
 *     summary: مصادقة المستخدم عبر Telegram
 *     description: يستقبل بيانات المستخدم من Telegram، يتحقق منها، ثم ينشئ حسابًا جديدًا أو يسجل دخول مستخدم حالي، ويعيد توكن JWT.
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - first_name
 *               - auth_date
 *               - hash
 *             properties:
 *               id:
 *                 type: number
 *                 description: معرف المستخدم في Telegram
 *                 example: 123456789
 *               first_name:
 *                 type: string
 *                 description: الاسم الأول للمستخدم
 *                 example: Ahmed
 *               last_name:
 *                 type: string
 *                 description: الاسم الأخير للمستخدم (اختياري)
 *                 example: Ali
 *               username:
 *                 type: string
 *                 description: اسم المستخدم في Telegram (اختياري)
 *                 example: ahmed_ali
 *               photo_url:
 *                 type: string
 *                 description: رابط صورة الملف الشخصي (اختياري)
 *                 example: "https://t.me/i/userpic/320/username.jpg"
 *               auth_date:
 *                 type: number
 *                 description: تاريخ المصادقة بصيغة Unix timestamp
 *                 example: 1672531200
 *               hash:
 *                 type: string
 *                 description: سلسلة الهاش للتحقق من صحة البيانات
 *                 example: "a1b2c3d4e5f6g7h8i9j0"
 *     responses:
 *       "200":
 *         description: مصادقة ناجحة. يتم إرجاع بيانات المستخدم مع توكن JWT
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 token:
 *                   type: string
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     telegramId:
 *                       type: number
 *                       example: 123456789
 *                     firstName:
 *                       type: string
 *                       example: "Ahmed"
 *                     lastName:
 *                       type: string
 *                       example: "Ali"
 *                     username:
 *                       type: string
 *                       example: "ahmed_ali"
 *                     photoUrl:
 *                       type: string
 *                       example: "https://t.me/i/userpic/320/username.jpg"
 *       "400":
 *         description: بيانات الطلب غير مكتملة أو غير صالحة
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "بيانات غير صالحة"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                       message:
 *                         type: string
 *       "401":
 *         description: فشل التحقق من الـ hash (البيانات غير موثوقة)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "فشل التحقق من البيانات"
 *       "429":
 *         description: محاولات مصادقة كثيرة جدًا
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "محاولات مصادقة كثيرة جدًا، يرجى المحاولة مرة أخرى بعد 15 دقيقة"
 */
router.post(
    '/auth/telegram',
    (req, res, next) => {
        console.log('Telegram auth request received:', req.body);
        next();
    },
    authLimiter,
    validateTelegramAuth,
    handleValidationErrors,
    authWithTelegram
);

/**
 * @swagger
 * /api/users/auth/telegram/callback:
 *   get:
 *     summary: معالج إعادة التوجيه من Telegram OAuth
 *     description: يستقبل بيانات المستخدم من Telegram عبر query parameters، يتحقق منها، ثم يعيد توجيه المستخدم إلى الواجهة الأمامية مع توكن JWT.
 *     tags: [Users]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: number
 *         description: معرف المستخدم في Telegram
 *       - in: query
 *         name: first_name
 *         required: true
 *         schema:
 *           type: string
 *         description: الاسم الأول للمستخدم
 *       - in: query
 *         name: last_name
 *         schema:
 *           type: string
 *         description: الاسم الأخير للمستخدم (اختياري)
 *       - in: query
 *         name: username
 *         schema:
 *           type: string
 *         description: اسم المستخدم في Telegram (اختياري)
 *       - in: query
 *         name: auth_date
 *         required: true
 *         schema:
 *           type: number
 *         description: تاريخ المصادقة بصيغة Unix timestamp
 *       - in: query
 *         name: hash
 *         required: true
 *         schema:
 *           type: string
 *         description: سلسلة الهاش للتحقق من صحة البيانات
 *     responses:
 *       "302":
 *         description: إعادة توجيه إلى الواجهة الأمامية مع توكن أو رسالة خطأ
 *       "500":
 *         description: خطأ في الخادم
 */
router.get('/auth/telegram/callback', telegramAuthCallback);

/**
 * @swagger
 * /api/users/profile:
 *   get:
 *     summary: الحصول على ملف المستخدم الشخصي
 *     description: يسترجع بيانات المستخدم الحالي
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "200":
 *         description: تم استرجاع بيانات المستخدم بنجاح
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     telegramId:
 *                       type: number
 *                       example: 123456789
 *                     firstName:
 *                       type: string
 *                       example: "Ahmed"
 *                     lastName:
 *                       type: string
 *                       example: "Ali"
 *                     username:
 *                       type: string
 *                       example: "ahmed_ali"
 *                     photoUrl:
 *                       type: string
 *                       example: "https://t.me/i/userpic/320/username.jpg"
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-01-01T00:00:00.000Z"
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-01-01T00:00:00.000Z"
 *       "401":
 *         description: غير مصرح بالوصول - توكن غير صالح أو مفقود
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "غير مصرح بالوصول"
 *   put:
 *     summary: تحديث ملف المستخدم الشخصي
 *     description: يحدث بيانات المستخدم الحالي
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: الاسم الأول للمستخدم
 *                 example: "Ahmed"
 *               lastName:
 *                 type: string
 *                 description: الاسم الأخير للمستخدم
 *                 example: "Ali"
 *               username:
 *                 type: string
 *                 description: اسم المستخدم في Telegram
 *                 example: "ahmed_ali"
 *               photoUrl:
 *                 type: string
 *                 description: رابط صورة الملف الشخصي
 *                 example: "https://t.me/i/userpic/320/username.jpg"
 *     responses:
 *       "200":
 *         description: تم تحديث بيانات المستخدم بنجاح
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "تم تحديث البيانات بنجاح"
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439011"
 *                     telegramId:
 *                       type: number
 *                       example: 123456789
 *                     firstName:
 *                       type: string
 *                       example: "Ahmed"
 *                     lastName:
 *                       type: string
 *                       example: "Ali"
 *                     username:
 *                       type: string
 *                       example: "ahmed_ali"
 *                     photoUrl:
 *                       type: string
 *                       example: "https://t.me/i/userpic/320/username.jpg"
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-01-01T00:00:00.000Z"
 *       "400":
 *         description: بيانات الطلب غير صالحة
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "بيانات غير صالحة"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                       message:
 *                         type: string
 *       "401":
 *         description: غير مصرح بالوصول - توكن غير صالح أو مفقود
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "غير مصرح بالوصول"
 */
router.route('/profile')
    .get(protect, getUserProfile)
    .put(
        protect,
        validateProfileUpdate,
        handleValidationErrors,
        updateUserProfile
    );

/**
 * @swagger
 * /api/users/stats:
 *   get:
 *     summary: الحصول على إحصائيات المستخدم
 *     description: يسترجع إحصائيات أداء المستخدم مثل عدد الاختبارات المكتملة ومتوسط الدرجات.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "200":
 *         description: تم استرجاع الإحصائيات بنجاح.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalTests:
 *                   type: integer
 *                   example: 5
 *                 averageScore:
 *                   type: integer
 *                   example: 85
 *                 bestScore:
 *                   type: integer
 *                   example: 100
 *       "401":
 *         description: غير مصرح بالوصول.
 */
router.get('/stats', protect, getUserStats);

module.exports = router;