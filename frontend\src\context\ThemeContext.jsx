// استيراد هوكس React الأساسية لإنشاء السياق وإدارة الحالة
import { createContext, useContext, useState, useEffect, useCallback } from 'react';

// إنشاء سياق المظهر (ThemeContext)
// سيتم استخدام هذا السياق لتوفير حالة المظهر ووظائف تبديله لجميع المكونات الفرعية.
const ThemeContext = createContext();

/**
 * هوك مخصص (Custom Hook) `useTheme`.
 * يوفر طريقة سهلة للوصول إلى سياق المظهر من أي مكون وظيفي.
 * 
 * @returns {object} - كائن يحتوي على حالة المظهر ودالة تبديل المظهر.
 * @throws {Error} - يرمي خطأ إذا لم يتم استخدام الهوك داخل مكون `ThemeProvider`.
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  // التحقق للتأكد من أن الهوك يستخدم داخل مزود السياق
  if (!context) {
    throw new Error('يجب استخدام useTheme داخل مكون ThemeProvider');
  }
  return context;
};

/**
 * مكون `ThemeProvider`.
 * يدير حالة المظهر (فاتح/داكن) ويضمن استمرارية اختيار المستخدم عبر التخزين المحلي.
 * 
 * @param {object} props - خصائص المكون.
 * @param {React.ReactNode} props.children - المكونات الفرعية التي ستتمكن من الوصول إلى سياق المظهر.
 * @returns {JSX.Element} - مزود سياق المظهر.
 */
export const ThemeProvider = ({ children }) => {
  // تهيئة حالة المظهر من التخزين المحلي (localStorage) أو تعيينها إلى 'light' كقيمة افتراضية.
  const [theme, setTheme] = useState(() => {
    try {
      const savedTheme = localStorage.getItem('tamruh-theme');
      // التحقق مما إذا كان المظهر المحفوظ صالحًا ('light' أو 'dark')
      return savedTheme && ['light', 'dark'].includes(savedTheme) ? savedTheme : 'light';
    } catch (error) {
      // في حالة حدوث خطأ أثناء الوصول إلى localStorage، يتم تعيين المظهر الافتراضي
      return 'light';
    }
  });

  /**
   * دالة `toggleTheme`.
   * تقوم بتبديل المظهر بين 'light' و 'dark'.
   * تم استخدام `useCallback` لتحسين الأداء ومنع إعادة إنشاء الدالة في كل مرة يتم فيها إعادة عرض المكون.
   */
  const toggleTheme = useCallback(() => {
    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
  }, []); // مصفوفة تبعيات فارغة تعني أن الدالة لن تتغير أبدًا

  // تأثير جانبي (useEffect) لتطبيق فئة المظهر على `document.body` وحفظها في التخزين المحلي.
  useEffect(() => {
    try {
      // إزالة أي فئات مظهر موجودة مسبقًا من جسم المستند
      document.body.classList.remove('theme-light', 'theme-dark');
      
      // إضافة فئة المظهر الحالية إلى جسم المستند
      if (theme === 'dark') {
        document.body.classList.add('theme-dark');
      } else {
        document.body.classList.add('theme-light'); // إضافة فئة 'theme-light' للوضوح
      }
      
      // حفظ تفضيل المظهر في التخزين المحلي لضمان استمراريته
      localStorage.setItem('tamruh-theme', theme);
    } catch (error) {
      // التعامل مع أخطاء تطبيق المظهر بصمت (مثل عدم توفر localStorage)
    }
  }, [theme]); // يتم تشغيل هذا التأثير كلما تغيرت قيمة `theme`

  // تأثير جانبي (useEffect) للتنظيف عند إلغاء تحميل المكون.
  // يضمن إزالة فئات المظهر من `document.body` عند إزالة المكون من DOM.
  useEffect(() => {
    return () => {
      try {
        document.body.classList.remove('theme-light', 'theme-dark');
      } catch (error) {
        // تجاهل أخطاء التنظيف
      }
    };
  }, []); // مصفوفة تبعيات فارغة تعني أن هذا التأثير يعمل مرة واحدة فقط عند تحميل المكون

  // الكائن الذي سيتم توفيره عبر سياق المظهر للمكونات الفرعية
  const value = {
    theme, // المظهر الحالي ('light' أو 'dark')
    toggleTheme, // دالة لتبديل المظهر
    isDark: theme === 'dark', // هل المظهر داكن؟ (قيمة منطقية)
    isLight: theme === 'light' // هل المظهر فاتح؟ (قيمة منطقية)
  };

  return (
    // توفير الكائن `value` لجميع المكونات الفرعية
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// تصدير سياق المظهر نفسه (ThemeContext)
export default ThemeContext;