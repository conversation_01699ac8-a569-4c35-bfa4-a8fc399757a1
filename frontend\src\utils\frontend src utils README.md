# أدوات الواجهة الأمامية (Frontend Utilities)

يحتوي هذا المجلد على دوال مساعدة وأدوات عامة تستخدم في جميع أنحاء الواجهة الأمامية للتطبيق. هذه الأدوات مصممة لتوفير وظائف متكررة، مثل إجراء طلبات API، بطريقة مركزية وقابلة لإعادة الاستخدام.

## هيكل المجلد

يتكون المجلد حاليًا من ملف JavaScript واحد يختص بتغليف طلبات API.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `api.js`
يوفر هذا الملف مجموعة من الدوال المساعدة لإجراء طلبات HTTP إلى الواجهة الخلفية (API). يتضمن غلافًا لدالة `fetch` الأصلية مع معالجة محسّنة للأخطاء وتكامل مع Sentry لتتبع الأخطاء. كما يوفر دوال مخصصة لطلبات GET، POST، PUT، DELETE، بالإضافة إلى إصدارات مصادق عليها من هذه الطلبات التي تضيف تلقائيًا توكن المصادقة إلى الرؤوس.
