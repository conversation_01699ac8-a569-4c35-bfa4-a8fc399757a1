[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:1.  **Create Custom Hook:** DESCRIPTION:-   Create a new file `frontend/src/hooks/useUserStats.js`.     -   This hook should accept `user.token` and `user.id` as parameters.     -   Inside the hook, use `useState` to manage `stats` (default: null), `loading` (default: true), and `error` (default: null).     -   Use `useEffect` to fetch user statistics from the `/api/users/stats` endpoint using `authenticatedGet`.     -   The hook should return an object: `{ stats, loading, error }`.     -   Ensure proper error handling. If the `authenticatedGet` call fails, set the `error` state and log the error.
-[ ] NAME:2.  **Refactor `DashboardPage.jsx` to use the Hook:** DESCRIPTION:-   Remove the `useState` hooks for `userStats` and the `useEffect` hook for data fetching from `DashboardPage.jsx`.     -   Import the new `useUserStats` hook: `import useUserStats from '../hooks/useUserStats';`.     -   Call the hook inside the component: `const { stats, loading, error: statsError } = useUserStats(user?.token, user?._id);`.     -   Update the `UserStats` component call to pass the `stats` and `loading` states from the hook. The `UserStats` component should be responsible for displaying its own loading state, so you'll need to slightly modify it to accept and use the `loading` prop.     -   Update the error handling logic. The `statsError` from the hook should be displayed in a relevant part of the UI, separate from the `handleStartTest` error.
-[ ] NAME:3.  **Setup Vitest:** DESCRIPTION:-   Assume `Vitest` is configured for the project (you can use a virtual DOM library like `@testing-library/react`).     -   The testing environment should be set up to mock API calls (`fetch` or the `authenticatedGet` function).
-[ ] NAME:4.  **Write Tests for `WelcomeHeader.jsx`:** DESCRIPTION:-   Create `frontend/src/components/WelcomeHeader.test.jsx`.     -   Test that the component renders the correct welcome message with the user's name (`fullName` or `username`).     -   Test that the user's avatar displays the correct first letter.
-[ ] NAME:5.  **Write Tests for `UserStats.jsx`:** DESCRIPTION:-   Create `frontend/src/components/UserStats.test.jsx`.     -   Test the loading state: when `stats` is null, the "جاري تحميل الإحصائيات..." message is displayed.     -   Test the no-data state: when `totalTests` is 0, the "لا توجد إحصائيات متاحة بعد..." message is displayed.     -   Test the success state: when `stats` data is provided, the component renders the correct values for `totalTests`, `averageScore`, and `bestScore`.
-[ ] NAME:6.  **Write Tests for `useUserStats.js`:** DESCRIPTION:-   Create `frontend/src/hooks/useUserStats.test.js`.     -   Test the success case: mock a successful API response and verify that the hook returns the correct `stats` and `loading: false`.     -   Test the error case: mock an API failure and verify that the hook returns `stats: null`, `loading: false`, and an `error` object.     -   Test that the hook does not make an API call if `user.token` is not provided.

