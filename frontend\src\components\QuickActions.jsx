/**
 * مكون QuickActions (الإجراءات السريعة).
 * يعرض الإجراءات السريعة المتاحة للمستخدم مثل بدء اختبار جديد.
 * 
 * @param {Object} props - خصائص المكون
 * @param {Function} props.handleStartTest - دالة بدء الاختبار
 * @param {boolean} props.isStartingTest - حالة تحميل بدء الاختبار
 * @param {string} props.error - رسالة الخطأ إن وجدت
 * @returns {JSX.Element} - مكون الإجراءات السريعة
 */
const QuickActions = ({ handleStartTest, isStartingTest, error }) => {
  return (
    <>
      {/* قسم الإجراءات السريعة: زر بدء اختبار جديد */}
      <section className="quick-actions">
        <div className="action-card primary-action">
          <button
            onClick={handleStartTest}
            disabled={isStartingTest} // تعطيل الزر أثناء بدء الاختبار
            className="btn-modern btn-primary-modern start-test-btn-modern"
          >
            {isStartingTest ? (
              <>
                <div className="btn-spinner"></div>
                جاري بدء الاختبار...
              </>
            ) : (
              <>
                <span className="btn-icon">🚀</span>
                بدء اختبار جديد
              </>
            )}
          </button>
        </div>
      </section>

      {/* رسالة الخطأ (تظهر إذا كان هناك خطأ) */}
      {error && (
        <div className="error-message mb-lg">
          <strong>خطأ:</strong> {error}
        </div>
      )}
    </>
  );
};

export default QuickActions;
