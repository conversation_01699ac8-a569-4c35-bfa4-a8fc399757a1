# مدققات الواجهة الخلفية (Backend Validators)

يحتوي هذا المجلد على دوال التحقق من صحة البيانات (validation functions) التي تستخدم مع مكتبة `express-validator`. هذه الدوال تحدد القواعد التي يجب أن تتبعها البيانات الواردة في الطلبات (مثل جسم الطلب أو معلمات الاستعلام) لضمان سلامة البيانات وتجنب الأخطاء قبل معالجتها بواسطة دوال التحكم (controllers).

## هيكل المجلد

يتكون المجلد من ملفات JavaScript، حيث يختص كل ملف بتعريف قواعد التحقق لنوع معين من البيانات أو الكيانات.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `userValidator.js`
يحتوي على قواعد التحقق من صحة البيانات المتعلقة بالمستخدمين. يتضمن قواعد للتحقق من بيانات المصادقة القادمة من Telegram (مثل `id`, `first_name`, `hash`)، بالإضافة إلى قواعد للتحقق من صحة البيانات عند تحديث الملف الشخصي للمستخدم (مثل `email`, `fullName`).
