// استيراد المكتبات والمكونات الضرورية
import React from 'react';
import { Link } from 'react-router-dom'; // مكون لإنشاء روابط تنقل داخل التطبيق
import { useAuth } from '../context/AuthContext'; // हुक للوصول إلى بيانات المصادقة
import TelegramLogin from './TelegramLogin'; // مكون زر تسجيل الدخول عبر تليجرام
import LogoutButton from './LogoutButton'; // مكون زر تسجيل الخروج
import ThemeSwitcher from './ThemeSwitcher'; // مكون تبديل المظهر (فاتح/داكن)
import './Navbar.css'; // استيراد ملف التنسيقات الخاص بالشريط

/**
 * مكون شريط التنقل (Navbar).
 * يعرض الشريط العلوي للتطبيق الذي يحتوي على روابط التنقل،
 * زر تسجيل الدخول/الخروج، ومفتاح تبديل المظهر.
 * 
 * @returns {JSX.Element} - شريط التنقل.
 */
const Navbar = () => {
  // الحصول على بيانات المستخدم ودالة تسجيل الخروج من سياق المصادقة
  const { user, logout } = useAuth();

  return (
    // حاوية شريط التنقل الرئيسية
    <nav className="navbar-container">
      {/* الجزء الأيسر من شريط التنقل */}
      <div className="navbar-left">
        {
          // عرض زر تسجيل الخروج إذا كان المستخدم مسجلاً للدخول،
          // وإلا عرض زر تسجيل الدخول عبر تليجرام.
          user ? <LogoutButton onLogout={logout} /> : <TelegramLogin />
        }
        {/* فاصل لإضافة مسافة بين العناصر */}
        <div className="navbar-item-spacer"></div>
        {/* مكون تبديل المظهر */}
        <ThemeSwitcher />
      </div>

      {/* الجزء الأيمن من شريط التنقل */}
      <div className="navbar-right">
        {/* رابط للعودة إلى الصفحة الرئيسية، يعرض اسم التطبيق */}
        <Link to="/" className="navbar-brand">
          Tumrah
        </Link>
      </div>
    </nav>
  );
};

// تصدير المكون ليكون متاحًا للاستخدام
export default Navbar;
