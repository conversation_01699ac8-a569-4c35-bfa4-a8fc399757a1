// backend/models/TestSession.js

// استيراد مكتبة Mongoose للتعامل مع MongoDB
const mongoose = require('mongoose');
const { Schema } = mongoose; // استخراج كائن Schema من Mongoose

/**
 * `testSessionSchema` (نموذج جلسة الاختبار).
 * يحدد بنية المستند لجلسة اختبار واحدة يقوم بها المستخدم.
 * 
 * @property {mongoose.Schema.Types.ObjectId} user - معرف المستخدم الذي أجرى الاختبار.
 *   - `ref: 'User'` - يشير إلى نموذج المستخدم.
 *   - `required: true` - إلزامي.
 *   - `index: true` - إنشاء فهرس على هذا الحقل لتحسين أداء الاستعلامات.
 * 
 * @property {Array<mongoose.Schema.Types.ObjectId>} questions - مصفوفة من معرفات الأسئلة في هذه الجلسة.
 *   - `ref: 'Question'` - يشير إلى نموذج السؤال.
 *   - `required: true` - إلزامي.
 * 
 * @property {Map<string, string>} userAnswers - خريطة (Map) لتخزين إجابات المستخدم.
 *   - المفتاح هو معرف السؤال (`_id` الخاص بالسؤال).
 *   - القيمة هي إجابة المستخدم (مثل 'A', 'B', 'C', 'D').
 *   - `default: {}` - القيمة الافتراضية هي كائن فارغ.
 * 
 * @property {string} status - حالة جلسة الاختبار.
 *   - `enum: ['in-progress', 'completed']` - القيم المسموح بها هي 'in-progress' أو 'completed'.
 *   - `default: 'in-progress'` - الحالة الافتراضية عند إنشاء الجلسة.
 * 
 * @property {number} score - درجة الاختبار (من 0 إلى 100).
 *   - `min: 0`, `max: 100` - نطاق الدرجة.
 * 
 * @property {Date} startedAt - تاريخ ووقت بدء جلسة الاختبار.
 *   - `default: Date.now` - يتم تعيينه تلقائيًا إلى الوقت الحالي عند إنشاء المستند.
 * 
 * @property {Date} completedAt - تاريخ ووقت انتهاء جلسة الاختبار.
 *   - يتم تعيينه عند اكتمال الاختبار.
 * 
 * @options
 *   `timestamps: true` - يضيف حقلي `createdAt` و `updatedAt` تلقائيًا إلى المستند.
 */
const testSessionSchema = new Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true,
    },
    questions: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Question',
        required: true,
    }],
    userAnswers: {
        type: Map,
        of: String, // المفتاح هو ID السؤال والقيمة هي إجابة المستخدم ('A', 'B', ..)
        default: {},
    },
    status: {
        type: String,
        enum: ['in-progress', 'completed'],
        default: 'in-progress',
    },
    score: {
        type: Number,
        min: 0,
        max: 100,
    },
    startedAt: {
        type: Date,
        default: Date.now,
    },
    completedAt: {
        type: Date,
    },
}, {
    timestamps: true, // لإضافة createdAt و updatedAt تلقائيًا
});

// إنشاء نموذج TestSession من المخطط
const TestSession = mongoose.model('TestSession', testSessionSchema);

// تصدير النموذج لاستخدامه في أجزاء أخرى من التطبيق
module.exports = TestSession;
