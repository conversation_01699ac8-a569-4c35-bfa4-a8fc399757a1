// backend/config/sentry.js

// استيراد مكتبة Sentry الخاصة بـ Node.js
const Sentry = require('@sentry/node');
// استيراد تكامل التنميط (profiling) لـ Node.js
const { nodeProfilingIntegration } = require('@sentry/profiling-node');

/**
 * دالة `initSentry`.
 * تهيئة Sentry لمراقبة الأخطاء وتتبع الأداء والتنميط في الواجهة الخلفية.
 * 
 * @returns {boolean} - true إذا تم تهيئة Sentry بنجاح، وإلا false.
 */
const initSentry = () => {
  // يتم تهيئة Sentry فقط إذا تم توفير DSN (Data Source Name) في متغيرات البيئة
  if (!process.env.SENTRY_DSN) {
    console.warn('لم يتم العثور على SENTRY_DSN في متغيرات البيئة. لن يتم تهيئة Sentry.');
    return false;
  }

  // بدء تهيئة Sentry مع الإعدادات المحددة
  Sentry.init({
    dsn: process.env.SENTRY_DSN, // مفتاح DSN الخاص بمشروع Sentry

    // تعيين بيئة التطبيق (مثل 'production', 'development')
    environment: process.env.NODE_ENV || 'development',

    // مراقبة الأداء (Performance monitoring)
    // تحديد نسبة العينات للمعاملات (traces)
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

    // التنميط (Profiling)
    // تحديد نسبة العينات للتنميط
    profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

    // التكاملات (Integrations)
    integrations: [
      // إضافة تكامل التنميط لـ Node.js
      nodeProfilingIntegration(),

      // تكامل HTTP لتتبع طلبات HTTP الصادرة والواردة
      Sentry.httpIntegration({
        tracing: true, // تفعيل تتبع التوزيع (distributed tracing)
      }),

      // تكامل Express.js لتتبع الأخطاء والأداء في تطبيقات Express
      Sentry.expressIntegration({
        /**
         * دالة `shouldCreateSpanForRequest`.
         * تحدد ما إذا كان يجب إنشاء span (جزء من التتبع) لطلب معين.
         * 
         * @param {string} url - عنوان URL للطلب.
         * @returns {boolean} - true لإنشاء span، false لتجاهل الطلب.
         */
        shouldCreateSpanForRequest: (url) => {
          // عدم إنشاء spans لطلبات فحص الصحة (health checks) والأصول الثابتة (API docs)
          return !url.includes('/health') && !url.includes('/api-docs');
        },
      }),

      // تكامل MongoDB لتتبع استعلامات قاعدة البيانات (يتطلب Mongoose)
      Sentry.mongoIntegration({
        useMongoose: true, // تفعيل التكامل مع Mongoose
      }),
    ],

    // تصفية الأخطاء قبل إرسالها إلى Sentry
    beforeSend(event, hint) {
      // تصفية بعض الأخطاء في وضع التطوير
      if (process.env.NODE_ENV === 'development') {
        // عدم إرسال أخطاء التحقق من الصحة (ValidationError) في وضع التطوير
        if (event.exception?.values?.[0]?.type === 'ValidationError') {
          return null;
        }
      }

      // تصفية أخطاء طلبات فحص الصحة
      if (event.request?.url?.includes('/health')) {
        return null;
      }

      // إضافة سياق إضافي وتنظيف البيانات الحساسة من الطلب
      if (event.request) {
        if (event.request.data) {
          const sanitizedData = { ...event.request.data };
          // حذف البيانات الحساسة قبل إرسالها إلى Sentry
          delete sanitizedData.password;
          delete sanitizedData.token;
          delete sanitizedData.jwt;
          event.request.data = sanitizedData;
        }
      }

      return event; // إرسال الحدث إلى Sentry إذا لم يتم تصفيته
    },

    // تتبع إصدار التطبيق (يستخدم إصدار الحزمة من package.json)
    release: process.env.npm_package_version || '1.0.0',

    // تفعيل وضع التصحيح (debug mode) في وضع التطوير
    debug: process.env.NODE_ENV === 'development',

    // التقاط الرفض غير المعالج للوعود (unhandled promise rejections)
    captureUnhandledRejections: true,

    // التقاط الاستثناءات غير الملتقطة (uncaught exceptions)
    captureUncaughtException: true,
  });

  console.log(`تم تهيئة Sentry لبيئة ${process.env.NODE_ENV}`);
  return true;
};

/**
 * دالة `captureErrorWithContext`.ل
 * التقاط خطأ مع سياق إضافي.
 * تسمح هذه الدالة بإرسال الأخطاء إلى Sentry مع معلومات إضافية مفيدة.
 * 
 * @param {Error} error - الخطأ المراد التقاطه.
 * @param {object} [options] - خيارات إضافية.
 * @param {object} [options.context={}] - معلومات سياقية إضافية (مثل المكون، العملية).
 * @param {object} [options.user=null] - معلومات المستخدم المرتبطة بالخطأ.
 * @param {object} [options.request=null] - معلومات الطلب المرتبطة بالخطأ.
 */
const captureErrorWithContext = (error, { context = {}, user = null, request = null } = {}) => {
  // تخطي إذا لم يتم تهيئة Sentry
  if (!process.env.SENTRY_DSN) {
    return;
  }

  Sentry.withScope((scope) => {
    // إضافة سياق المستخدم إذا تم توفيره
    if (user) {
      scope.setUser({
        id: user._id || user.id,
        email: user.email,
        username: user.username,
        role: user.role,
      });
    }

    // إضافة سياق الطلب إذا تم توفيره
    if (request) {
      scope.setContext('request', {
        method: request.method,
        url: request.originalUrl || request.url,
        headers: {
          'user-agent': request.get('user-agent'),
          'content-type': request.get('content-type'),
        },
        ip: request.ip,
        body: request.body ? JSON.stringify(request.body).substring(0, 1000) : undefined, // تحويل الجسم إلى JSON وتقليمه
      });
    }

    // إضافة سياق مخصص إذا تم توفيره
    if (Object.keys(context).length > 0) {
      scope.setContext('custom', context);
    }

    // إضافة علامات (tags) لتحسين التصفية والبحث في Sentry
    scope.setTag('component', context.component || 'backend');
    scope.setTag('operation', context.operation || 'unknown');

    // التقاط الخطأ
    Sentry.captureException(error);
  });
};

/**
 * دالة `captureMessageWithContext`.ل
 * التقاط رسالة مع سياق إضافي.
 * تستخدم لإرسال رسائل غير خطأ إلى Sentry لأغراض المراقبة أو التصحيح.
 * 
 * @param {string} message - الرسالة المراد التقاطها.
 * @param {string} [level='info'] - مستوى الخطورة (مثل 'info', 'warning', 'error', 'fatal').
 * @param {object} [context={}] - معلومات سياقية إضافية.
 */
const captureMessageWithContext = (message, level = 'info', context = {}) => {
  // تخطي إذا لم يتم تهيئة Sentry
  if (!process.env.SENTRY_DSN) {
    return;
  }

  Sentry.withScope((scope) => {
    if (Object.keys(context).length > 0) {
      scope.setContext('custom', context);
    }

    Sentry.captureMessage(message, level);
  });
};

/**
 * دالة `setUserContext`.ل
 * تعيين سياق المستخدم للنطاق الحالي في Sentry.
 * 
 * @param {object} user - معلومات المستخدم (مثل id, email, username, role).
 */
const setUserContext = (user) => {
  // تخطي إذا لم يتم تهيئة Sentry
  if (!process.env.SENTRY_DSN) {
    return;
  }

  Sentry.setUser({
    id: user._id || user.id,
    email: user.email,
    username: user.username,
    role: user.role,
  });
};

/**
 * دالة `clearUserContext`.ل
 * مسح سياق المستخدم من Sentry.
 */
const clearUserContext = () => {
  // تخطي إذا لم يتم تهيئة Sentry
  if (!process.env.SENTRY_DSN) {
    return;
  }

  Sentry.setUser(null);
};

// تصدير الدوال والوحدات النمطية لاستخدامها في أجزاء أخرى من التطبيق
module.exports = {
  initSentry,
  captureErrorWithContext,
  captureMessageWithContext,
  setUserContext,
  clearUserContext,
  Sentry, // تصدير كائن Sentry نفسه
  /**
   * دالة `getSentryHandlers`.ل
   * توفر وصولاً مشروطًا إلى معالجات Sentry (مثل معالجات Express).
   * 
   * @returns {object|null} - كائن معالجات Sentry إذا تم تهيئة Sentry، وإلا null.
   */
  getSentryHandlers: () => {
    if (!process.env.SENTRY_DSN) {
      return null;
    }
    return Sentry.Handlers;
  },
};