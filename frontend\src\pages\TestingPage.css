/* TestingPage.css - تنسيقات صفحة الاختبار */

/* --- حاوية الصفحة الرئيسية للاختبار --- */
.quiz-container {
  min-height: 100vh; /* ارتفاع لا يقل عن 100% من ارتفاع نافذة العرض */
  background: linear-gradient(to bottom right, #eff6ff, #e0e7ff); /* خلفية متدرجة */
  display: flex; /* استخدام Flexbox للمحاذاة */
  align-items: center; /* محاذاة عمودية في المنتصف */
  justify-content: center; /* محاذاة أفقية في المنتصف */
  padding: 1rem; /* مسافة داخلية */
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'; /* خطوط النص */
}

/* --- البطاقة الرئيسية التي تحتوي على محتوى الاختبار --- */
.quiz-card {
  width: 100%; /* عرض كامل */
  max-width: 896px; /* أقصى عرض (max-w-4xl) */
  padding: 2rem; /* مسافة داخلية */
  background-color: white; /* لون الخلفية */
  border-radius: 0.75rem; /* حواف دائرية (rounded-xl) */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1); /* ظل للبطاقة */
  border: 1px solid #e5e7eb; /* حدود البطاقة */
}

/* --- قسم الرأس الذي يحتوي على التقدم والدرجة --- */
.progress-header {
  margin-bottom: 2rem; /* مسافة سفلية */
}

/* معلومات التقدم (السؤال الحالي والدرجة) */
.progress-info {
  display: flex; /* استخدام Flexbox */
  justify-content: space-between; /* توزيع المسافة بين العناصر */
  align-items: center; /* محاذاة عمودية */
  margin-bottom: 1rem; /* مسافة سفلية */
}

/* نص التقدم (مثل "السؤال 1 من 10") */
.progress-text {
  font-size: 0.875rem; /* حجم الخط (text-sm) */
  font-weight: 500; /* سمك الخط (font-medium) */
  color: #4b5563; /* لون النص (text-gray-600) */
}

/* نص الدرجة (مثل "الدرجة: 70%") */
.progress-score {
  font-size: 0.875rem; /* حجم الخط (text-sm) */
  font-weight: 500; /* سمك الخط (font-medium) */
  color: #4f46e5; /* لون النص (text-indigo-600) */
}

/* حاوية شريط التقدم */
.progress-bar-container {
  position: relative;
  width: 100%;
  overflow: hidden; /* إخفاء أي جزء يتجاوز الحدود */
  border-radius: 9999px; /* حواف دائرية بالكامل */
  background-color: rgba(79, 70, 229, 0.2); /* لون خلفية الشريط (bg-primary/20) */
  height: 0.5rem; /* ارتفاع الشريط (h-2) */
}

/* شريط التقدم الفعلي (الجزء الممتلئ) */
.progress-bar-fill {
  height: 100%; /* ارتفاع كامل للحاوية */
  background-color: #4f46e5; /* نفس لون نص الدرجة */
  transition: width 0.5s ease-in-out; /* حركة انتقال سلسة لتغير العرض */
}

/* --- قسم السؤال --- */
.question-container {
  margin-bottom: 2rem; /* مسافة سفلية */
}

/* عنوان السؤال */
.question-title {
  font-size: 1.5rem; /* حجم الخط (text-2xl) */
  font-weight: 700; /* سمك الخط (font-bold) */
  color: #1f2937; /* لون النص (text-gray-800) */
  text-align: center; /* محاذاة النص في المنتصف */
}

/* --- شبكة الخيارات (الإجابات) --- */
.options-grid {
  display: grid; /* استخدام Grid للتخطيط */
  grid-template-columns: 1fr; /* عمود واحد بعرض كامل */
  gap: 1rem; /* مسافة بين الخيارات */
}

/* تصميم متجاوب: عمودين على الشاشات المتوسطة فما فوق */
@media (min-width: 768px) { /* md: */
  .options-grid {
    grid-template-columns: repeat(2, 1fr); /* عمودين متساويين في العرض */
  }
}

/* --- بطاقة الخيار الفردي --- */
.option-card {
  padding: 1.5rem; /* مسافة داخلية */
  border: 1px solid #e5e7eb; /* حدود الخيار */
  border-radius: 0.75rem; /* حواف دائرية */
  cursor: pointer; /* مؤشر الفأرة على شكل يد */
  transition: all 0.3s ease-in-out; /* حركة انتقال سلسة */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1); /* ظل خفيف */
}

/* تأثير التحويم (hover) على بطاقة الخيار */
.option-card-hover:hover {
  transform: scale(1.05); /* تكبير الخيار قليلاً */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1); /* ظل أكبر */
  background-color: #f9fafb; /* تغيير لون الخلفية (hover:bg-gray-50) */
}

/* محتوى الخيار (نص الخيار) */
.option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* نص الخيار */
.option-text {
  font-size: 1.125rem; /* حجم الخط (text-lg) */
  font-weight: 600; /* سمك الخط (font-semibold) */
}

/* --- حالات الخيار بعد الإجابة --- */
/* الخيار الصحيح */
.option-card-correct {
  background-color: #d1fae5; /* خلفية خضراء فاتحة (bg-green-100) */
  border-color: #10b981; /* حدود خضراء (border-green-500) */
  color: #065f46; /* نص أخضر داكن (text-green-800) */
  border-width: 2px; /* سمك الحدود */
}

/* الخيار الخاطئ */
.option-card-incorrect {
  background-color: #fee2e2; /* خلفية حمراء فاتحة (bg-red-100) */
  border-color: #ef4444; /* حدود حمراء (border-red-500) */
  color: #991b1b; /* نص أحمر داكن (text-red-800) */
  border-width: 2px; /* سمك الحدود */
}

/* الخيار المعطل (بعد الإجابة) */
.option-card-disabled {
  opacity: 0.6; /* شفافية أقل */
  cursor: not-allowed; /* مؤشر الفأرة يشير إلى عدم السماح */
}

/* --- أيقونات ورسائل التغذية الراجعة --- */
.feedback-icon-container {
  display: flex;
  align-items: center;
}

/* أيقونة التغذية الراجعة (مثل علامة صح أو خطأ) */
.feedback-icon {
  width: 1.5rem; /* عرض (w-6) */
  height: 1.5rem; /* ارتفاع (h-6) */
  font-size: 1.5rem;
  line-height: 1.5rem;
}

/* لون أيقونة التغذية الراجعة الصحيحة */
.feedback-icon.correct {
  color: #16a34a; /* لون أخضر (text-green-600) */
}

/* لون أيقونة التغذية الراجعة الخاطئة */
.feedback-icon.incorrect {
  color: #dc2626; /* لون أحمر (text-red-600) */
}

/* حاوية رسالة التغذية الراجعة */
.feedback-message-container {
  margin-top: 1.5rem;
  text-align: center;
}

/* نص رسالة التغذية الراجعة */
.feedback-message {
  font-weight: 600; /* سمك الخط (font-semibold) */
  font-size: 1.125rem; /* حجم الخط (text-lg) */
}

/* لون رسالة التغذية الراجعة الصحيحة */
.feedback-message.correct {
  color: #16a34a; /* لون أخضر (text-green-600) */
}

/* لون رسالة التغذية الراجعة الخاطئة */
.feedback-message.incorrect {
  color: #dc2626; /* لون أحمر (text-red-600) */
}

/* --- تنسيقات شاشة النتائج النهائية --- */
.results-card {
  text-align: center; /* محاذاة النص في المنتصف */
}

/* حاوية أيقونة النتائج */
.results-icon-container {
    margin-bottom: 1rem;
}
/* أيقونة النتائج (مثل علامة صح كبيرة) */
.results-icon {
  display: inline-block;
  width: 4rem; /* عرض (w-16) */
  height: 4rem; /* ارتفاع (h-16) */
  line-height: 4rem;
  color: #22c55e; /* لون أخضر (text-green-500) */
  font-size: 3rem;
  border: 3px solid #22c55e;
  border-radius: 50%; /* شكل دائري */
  margin: 0 auto 1rem auto;
}

/* عنوان النتائج (مثل "نتائج الاختبار") */
.results-title {
  font-size: 1.875rem; /* حجم الخط (text-3xl) */
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

/* عنوان فرعي للنتائج */
.results-subtitle {
  font-size: 1.25rem; /* حجم الخط (text-xl) */
  color: #4b5563;
}

/* درجة الاختبار النهائية */
.results-score {
  font-size: 2.25rem; /* حجم الخط (text-4xl) */
  font-weight: 700;
  color: #4f46e5;
  margin-top: 0.5rem;
}

/* النسبة المئوية للنتائج */
.results-percentage {
  font-size: 1.125rem; /* حجم الخط (text-lg) */
  color: #6b7280;
  margin-top: 0.5rem;
  margin-bottom: 1.5rem;
}

/* أزرار النتائج (الأساسي والثانوي) */
.results-button, .results-button-secondary {
  display: inline-block;
  margin: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

/* تنسيق الزر الأساسي */
.results-button {
  background-color: #4f46e5; /* لون خلفية أزرق */
  color: white; /* لون النص أبيض */
}
.results-button:hover {
  background-color: #4338ca; /* لون خلفية أغمق عند التحويم */
}

/* تنسيق الزر الثانوي */
.results-button-secondary {
  background-color: #e5e7eb; /* لون خلفية رمادي فاتح */
  color: #1f2937; /* لون نص رمادي داكن */
}
.results-button-secondary:hover {
    background-color: #d1d5db; /* لون خلفية أغمق عند التحويم */
}
