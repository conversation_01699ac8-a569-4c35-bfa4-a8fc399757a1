// backend/validators/userValidator.js

// استيراد دالة `body` من مكتبة `express-validator` لتعريف قواعد التحقق لجسم الطلب
const { body } = require('express-validator');

/**
 * `validateTelegramAuth` (قواعد التحقق لبيانات مصادقة Telegram).
 * مجموعة من قواعد التحقق التي يتم تطبيقها على البيانات المستلمة من Telegram
 * عند محاولة المستخدم تسجيل الدخول أو التسجيل عبر Telegram.
 */
const validateTelegramAuth = [
    // التحقق من حقل 'id': يجب أن يكون موجودًا ورقميًا.
    body('id', 'معرف Telegram إلزامي ويجب أن يكون رقمًا').isNumeric(),
    // التحقق من حقل 'first_name': يجب ألا يكون فارغًا، يتم إزالة المسافات البيضاء الزائدة، ويتم تنظيفه (escape) لمنع هجمات XSS.
    body('first_name', 'الاسم الأول إلزامي').not().isEmpty().trim().escape(),
    // التحقق من حقل 'hash': إلزامي للتحقق من صحة بيانات Telegram.
    body('hash', 'الـ hash إلزامي للتحقق').not().isEmpty(),
    // تنظيف (sanitize) الحقول الاختيارية إذا كانت موجودة:
    // 'last_name': اختياري، يتم إزالة المسافات البيضاء الزائدة وتنظيفه.
    body('last_name').optional().trim().escape(),
    // 'username': اختياري، يتم إزالة المسافات البيضاء الزائدة وتنظيفه.
    body('username').optional().trim().escape(),
];

/**
 * `validateProfileUpdate` (قواعد التحقق لعملية تحديث الملف الشخصي).
 * مجموعة من قواعد التحقق التي يتم تطبيقها على البيانات عند محاولة المستخدم تحديث ملفه الشخصي.
 */
const validateProfileUpdate = [
    // التحقق من حقل 'email': اختياري، ولكن إذا تم إرساله، يجب أن يكون بريدًا إلكترونيًا صالحًا ويتم تطبيعه (normalize).
    body('email', 'الرجاء إدخال بريد إلكتروني صالح').optional().isEmail().normalizeEmail(),
    // التحقق من حقل 'fullName': اختياري، يتم إزالة المسافات البيضاء الزائدة وتنظيفه.
    body('fullName').optional().trim().escape(),
];

// تصدير مجموعات قواعد التحقق لاستخدامها في تعريفات المسارات
module.exports = {
    validateTelegramAuth,
    validateProfileUpdate,
};