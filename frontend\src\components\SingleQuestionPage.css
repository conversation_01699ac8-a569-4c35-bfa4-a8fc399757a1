/* SingleQuestionPage.css - تنسيقات صفحة السؤال الواحد */

/* --- تنسيقات بطاقة السؤال الرئيسية --- */
.question-card {
  background: var(--card-bg); /* لون خلفية البطاقة (متغير CSS) */
  border-radius: 16px; /* حواف دائرية */
  padding: 2rem; /* مسافة داخلية */
  margin-bottom: 2rem; /* مسافة سفلية بين البطاقات */
  box-shadow: 0 4px 16px var(--shadow-color); /* ظل للبطاقة */
  border: 1px solid var(--border-color); /* حدود البطاقة */
  transition: all 0.3s ease; /* حركة انتقال سلسة لجميع الخصائص */
}

/* تأثير عند مرور مؤشر الفأرة فوق بطاقة السؤال */
.question-card:hover {
  transform: translateY(-2px); /* رفع البطاقة قليلاً للأعلى */
  box-shadow: 0 8px 24px var(--shadow-color); /* زيادة حجم الظل */
}

/* تنسيقات عنوان السؤال داخل البطاقة */
.question-card h2 {
  font-family: 'Tajawal', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; /* خط النص */
  font-size: 1.5rem; /* حجم الخط */
  font-weight: 600; /* سمك الخط */
  color: var(--text-color); /* لون النص (متغير CSS) */
  margin-bottom: 1.5rem; /* مسافة سفلية */
  line-height: 1.6; /* ارتفاع السطر */
  text-align: center; /* محاذاة النص في المنتصف */
}

/* --- تنسيقات شبكة الخيارات (الإجابات) --- */
.options-grid {
  display: grid; /* استخدام Grid للتخطيط */
  grid-template-columns: 1fr; /* عمود واحد بعرض كامل */
  gap: 1rem; /* مسافة بين الخيارات */
  margin-top: 1.5rem; /* مسافة علوية */
}

/* --- تنسيقات كل خيار من خيارات الإجابة --- */
.option {
  background: var(--card-bg); /* لون خلفية الخيار */
  border: 2px solid var(--border-color); /* حدود الخيار */
  border-radius: 12px; /* حواف دائرية */
  padding: 1rem 1.5rem; /* مسافة داخلية */
  cursor: pointer; /* مؤشر الفأرة على شكل يد */
  transition: all 0.3s ease; /* حركة انتقال سلسة */
  font-family: 'Tajawal', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; /* خط النص */
  font-size: 1rem; /* حجم الخط */
  font-weight: 500; /* سمك الخط */
  color: var(--text-color); /* لون النص */
  text-align: center; /* محاذاة النص في المنتصف */
  position: relative; /* ضروري لتأثير التوهج */
  overflow: hidden; /* إخفاء أي جزء يتجاوز حدود الخيار */
}

/* تأثير التوهج عند مرور المؤشر (عنصر زائف) */
.option:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%; /* يبدأ من خارج العنصر على اليسار */
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(108, 92, 231, 0.1), transparent); /* تدرج لوني */
  transition: left 0.5s; /* حركة انتقال لخاصية left */
}

/* تأثير عند مرور مؤشر الفأرة فوق الخيار */
.option:hover {
  border-color: var(--primary-color); /* تغيير لون الحدود */
  background: var(--hover-bg); /* تغيير لون الخلفية عند التحويم */
  transform: translateY(-2px); /* رفع الخيار قليلاً */
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.15); /* إضافة ظل */
}

/* حركة التوهج عند مرور المؤشر */
.option:hover:before {
  left: 100%; /* يتحرك التوهج إلى اليمين */
}

/* --- تنسيقات الخيار الصحيح والخاطئ --- */
.option.correct {
  background: var(--correct); /* لون خلفية الخيار الصحيح */
  border-color: var(--correct); /* لون حدود الخيار الصحيح */
  color: white; /* لون النص أبيض */
  cursor: default; /* مؤشر الفأرة عادي */
}

.option.wrong {
  background: var(--wrong); /* لون خلفية الخيار الخاطئ */
  border-color: var(--wrong); /* لون حدود الخيار الخاطئ */
  color: white; /* لون النص أبيض */
  cursor: default; /* مؤشر الفأرة عادي */
}

/* إزالة تأثير التحويم على الخيارات الصحيحة والخاطئة */
.option.correct:hover,
.option.wrong:hover {
  transform: none; /* إزالة تأثير الرفع */
  box-shadow: 0 4px 16px var(--shadow-color); /* الحفاظ على الظل الافتراضي */
}

/* --- تصميم متجاوب (Responsive Design) --- */
@media (max-width: 768px) {
  .question-card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .question-card h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .option {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }

  .options-grid {
    gap: 0.75rem;
    margin-top: 1rem;
  }
}

/* --- تعديلات الوضع الداكن (Dark Theme) --- */
/* يتم تطبيق هذه التنسيقات عندما يكون العنصر الأب (مثل <body> أو <html>) لديه خاصية data-theme="dark" */
[data-theme="dark"] .question-card {
  background: var(--card-bg);
  border-color: var(--border-color);
}

[data-theme="dark"] .option {
  background: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

[data-theme="dark"] .option:hover {
  background: var(--hover-bg);
  border-color: var(--primary-color);
}
