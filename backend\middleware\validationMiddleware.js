// backend/middleware/validationMiddleware.js

// استيراد دالة `validationResult` من مكتبة `express-validator`
const { validationResult } = require('express-validator');

/**
 * دالة `handleValidationErrors`.
 * middleware لمعالجة أخطاء التحقق (validation errors) التي تم إنشاؤها بواسطة `express-validator`.
 * يتم استخدامها بعد قواعد التحقق في تعريف المسار.
 * 
 * @param {object} req - كائن الطلب من Express.
 * @param {object} res - كائن الاستجابة من Express.
 * @param {function} next - دالة رد النداء للانتقال إلى middleware التالي.
 * @returns {void} - تمرر التحكم أو ترسل استجابة JSON مع الأخطاء.
 */
const handleValidationErrors = (req, res, next) => {
    // `validationResult(req)` تقوم بجمع جميع أخطاء التحقق التي تم العثور عليها في الطلب
    const errors = validationResult(req);

    // إذا كانت هناك أخطاء (أي أن `errors` ليست فارغة)
    if (!errors.isEmpty()) {
        // نرسل استجابة 400 (طلب سيء) مع تفاصيل الأخطاء في جسم الاستجابة
        return res.status(400).json({ errors: errors.array() });
    }

    // إذا لم تكن هناك أخطاء في التحقق، ننتقل إلى الـ Middleware التالي في سلسلة الطلب
    // أو إلى دالة التحكم (controller function) الخاصة بالمسار.
    next();
};

// تصدير دالة middleware لمعالجة أخطاء التحقق
module.exports = { handleValidationErrors };
