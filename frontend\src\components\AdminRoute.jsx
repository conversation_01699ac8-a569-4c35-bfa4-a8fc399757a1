// استيراد المكونات اللازمة من مكتبة react-router-dom
import { Navigate, useLocation } from 'react-router-dom';
// استيراد हुक useAuth للوصول إلى بيانات المصادقة
import { useAuth } from '../context/AuthContext';

/**
 * مكون AdminRoute لحماية المسارات الخاصة بالمدير (Admin).
 * يتحقق هذا المكون مما إذا كان المستخدم قد قام بتسجيل الدخول ويمتلك صلاحيات "admin" أو "sudo".
 * 
 * @param {object} props - الخصائص المستلمة من المكون.
 * @param {React.ReactNode} props.children - المكونات الفرعية التي سيتم عرضها إذا تم التحقق من الصلاحيات.
 * @returns {JSX.Element} - يعرض المكونات الفرعية المحمية، أو يعيد التوجيه إلى صفحة أخرى.
 */
const AdminRoute = ({ children }) => {
    // الحصول على حالة المستخدم وحالة التحميل من سياق المصادقة
    const { user, loading } = useAuth();
    // الحصول على معلومات الموقع الحالي للتوجيه بعد تسجيل الدخول
    const location = useLocation();

    // عرض مؤشر تحميل أثناء التحقق من حالة المصادقة
    if (loading) {
        return (
            <div className="loading">
                <div className="spinner"></div>
                <span>يتم التحقق من الصلاحيات...</span>
            </div>
        );
    }

    // إذا لم يكن المستخدم مسجلاً للدخول، يتم إعادة توجيهه إلى الصفحة الرئيسية
    if (!user) {
        // Navigate يقوم بإعادة التوجيه مع حفظ الموقع الأصلي للعودة إليه بعد تسجيل الدخول
        return <Navigate to="/" state={{ from: location }} replace />;
    }

    // التحقق مما إذا كان المستخدم لديه صلاحيات "admin" أو "sudo"
    const isAdmin = user.role === 'admin' || user.role === 'sudo';
    
    // إذا لم يكن المستخدم مديرًا، يتم إعادة توجيهه إلى لوحة التحكم
    if (!isAdmin) {
        return <Navigate to="/dashboard" replace />;
    }

    // إذا تم التحقق من كل الشروط، يتم عرض المحتوى المحمي (المكونات الفرعية)
    return children;
};

// تصدير المكون لاستخدامه في أجزاء أخرى من التطبيق
export default AdminRoute;
