// backend/middleware/authMiddleware.js

// استيراد مكتبة `jsonwebtoken` للتعامل مع توكنات JWT
const jwt = require('jsonwebtoken');
// استيراد نموذج المستخدم للبحث عن المستخدمين
const User = require('../models/User');
// استيراد دوال Sentry لتعيين سياق المستخدم والتقاط الأخطاء
const { setUserContext, captureErrorWithContext } = require('../config/sentry');

/**
 * دالة `protect`.
 * middleware لحماية المسارات التي تتطلب مصادقة المستخدم.
 * تتحقق من وجود توكن JWT صالح في رأس الطلب، وتتحقق من صحة المستخدم.
 * 
 * @param {object} req - كائن الطلب من Express.
 * @param {object} res - كائن الاستجابة من Express.
 * @param {function} next - دالة رد النداء للانتقال إلى middleware التالي.
 * @returns {Promise<void>} - لا تعيد قيمة، بل تمرر التحكم أو ترسل استجابة.
 */
const protect = async (req, res, next) => {
    let token; // متغير لتخزين توكن المصادقة

    // التحقق مما إذا كان رأس `Authorization` موجودًا ويبدأ بـ 'Bearer'
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        try {
            // 1. استخراج التوكن من رأس `Authorization`
            token = req.headers.authorization.split(' ')[1];

            // 2. التحقق من صحة التوكن وفك تشفيره باستخدام المفتاح السري
            const decoded = jwt.verify(token, process.env.JWT_SECRET);

            // 3. البحث عن المستخدم في قاعدة البيانات باستخدام المعرف المستخرج من التوكن
            // واستبعاد حقل كلمة المرور (`-password`)
            req.user = await User.findById(decoded.id).select('-password');

            // 4. التحقق من وجود المستخدم
            if (!req.user) {
                // إرسال استجابة 401 (غير مصرح به) إذا لم يتم العثور على المستخدم
                return res.status(401).json({ message: 'غير مصرح به، المستخدم غير موجود' });
            }
            // 5. التحقق مما إذا كان حساب المستخدم محظورًا
            if (req.user.role === 'banned') {
                // إرسال استجابة 403 (ممنوع) إذا كان الحساب محظورًا
                return res.status(403).json({ message: 'حسابك محظور.' });
            }

            // تعيين سياق المستخدم في Sentry لتتبع الأخطاء بشكل أفضل
            setUserContext(req.user);

            next(); // الانتقال إلى middleware التالي أو معالج المسار
        } catch (error) {
            console.error('خطأ في middleware المصادقة (protect):', error.message);
            let statusCode = 401; // الحالة الافتراضية: غير مصرح به
            let message = 'غير مصرح به، فشل التوكن'; // رسالة الخطأ الافتراضية

            // تحديد رسالة وحالة الخطأ بناءً على نوع الخطأ
            if (error.message === 'حسابك محظور.') { // هذا الشرط لن يتم الوصول إليه إذا تم التعامل معه أعلاه
                statusCode = 403;
                message = error.message;
            } else if (error.name === 'TokenExpiredError') {
                message = 'غير مصرح به، انتهت صلاحية التوكن';
            } else if (error.name === 'JsonWebTokenError') {
                message = 'غير مصرح به، التوكن غير صالح';
            } else if (error.message === 'غير مصرح به، المستخدم غير موجود') { // هذا الشرط لن يتم الوصول إليه إذا تم التعامل معه أعلاه
                message = error.message;
            }

            // التقاط أخطاء المصادقة إلى Sentry (فقط لأخطاء الخادم 5xx، وليس فشل المصادقة من جانب العميل)
            if (statusCode >= 500 || (error.name !== 'TokenExpiredError' && error.name !== 'JsonWebTokenError')) {
                captureErrorWithContext(error, {
                    context: {
                        component: 'authMiddleware',
                        operation: 'protect',
                        statusCode,
                        errorName: error.name,
                    },
                    request: req, // تمرير كائن الطلب إلى Sentry
                });
            }

            // إرسال الاستجابة مباشرة بدلاً من رمي خطأ قد لا يتم التقاطه بشكل صحيح
            return res.status(statusCode).json({ message });
        }
    }

    // إذا لم يتم العثور على توكن في رأس الطلب أصلاً
    if (!token) {
        return res.status(401).json({ message: 'غير مصرح به، لا يوجد توكن' });
    }
};

/**
 * دالة `authorizeRoles`.ل
 * middleware لترخيص الوصول بناءً على أدوار المستخدم.
 * تقبل عددًا متغيرًا من الأدوار المسموح بها.
 * 
 * @param {...string} roles - الأدوار المسموح بها للوصول إلى المسار.
 * @returns {function} - دالة middleware.
 */
const authorizeRoles = (...roles) => {
    return (req, res, next) => {
        // التحقق مما إذا كان المستخدم موجودًا ولديه أحد الأدوار المسموح بها
        if (!req.user || !roles.includes(req.user.role)) {
            // إرسال استجابة 403 (ممنوع) إذا لم يكن المستخدم مصرحًا له
            return res.status(403).json({ message: `دور المستخدم '${req.user ? req.user.role : 'غير معروف'}' غير مصرح له بالوصول إلى هذا المسار. الأدوار المسموح بها: ${roles.join(', ')}` });
        }
        next(); // الانتقال إلى middleware التالي أو معالج المسار
    };
};

/**
 * دالة `authorizeSudo`.ل
 * middleware لترخيص الوصول فقط للمستخدمين ذوي دور 'sudo'.
 * 
 * @param {object} req - كائن الطلب.
 * @param {object} res - كائن الاستجابة.
 * @param {function} next - دالة رد النداء للانتقال إلى middleware التالي.
 * @returns {void} - تمرر التحكم أو ترسل استجابة.
 */
const authorizeSudo = (req, res, next) => {
    // التحقق مما إذا كان المستخدم موجودًا ودوره 'sudo'
    if (req.user && req.user.role === 'sudo') {
        next(); // الانتقال إلى middleware التالي
    } else {
        // إرسال استجابة 403 (ممنوع) إذا لم يكن المستخدم 'sudo'
        return res.status(403).json({ message: 'غير مصرح به، فقط المستخدمون ذوو صلاحيات Sudo يمكنهم تنفيذ هذا الإجراء.' });
    }
};

// تصدير جميع دوال middleware المصادقة والترخيص
module.exports = {
    protect,
    authorizeRoles,
    authorizeSudo
};