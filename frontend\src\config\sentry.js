// frontend/src/config/sentry.js

// استيراد مكتبة Sentry للواجهة الأمامية
import * as Sentry from '@sentry/react';
// استيراد هوك useEffect من React، يستخدم في تكامل Sentry مع React Router
import { useEffect } from 'react';

/**
 * تهيئة Sentry لمراقبة الأخطاء والأداء في الواجهة الأمامية.
 * يتم استدعاء هذه الدالة مرة واحدة عند بدء تشغيل التطبيق.
 */
export const initSentry = () => {
  // يتم تهيئة Sentry فقط إذا تم توفير DSN (Data Source Name)
  const dsn = import.meta.env.VITE_SENTRY_DSN;

  if (!dsn) {
    // في وضع التطوير، يتم عرض تحذير إذا لم يتم العثور على DSN
    if (import.meta.env.MODE === 'development') {
      console.warn('لم يتم العثور على VITE_SENTRY_DSN في متغيرات البيئة. لن يتم تهيئة Sentry.');
    }
    return; // إيقاف التهيئة إذا لم يتوفر DSN
  }

  // بدء تهيئة Sentry مع الإعدادات المحددة
  Sentry.init({
    dsn: dsn, // مفتاح DSN الخاص بمشروع Sentry

    // تعيين بيئة التطبيق (مثل 'production', 'development')
    environment: import.meta.env.MODE || 'development',

    // مراقبة الأداء (Performance monitoring)
    // تحديد نسبة العينات للمعاملات (traces)
    tracesSampleRate: import.meta.env.MODE === 'production' ? 0.1 : 1.0,

    // إعادة تشغيل الجلسة (Session replay)
    // تحديد نسبة العينات لتسجيل الجلسات
    replaysSessionSampleRate: import.meta.env.MODE === 'production' ? 0.1 : 1.0,
    // تحديد نسبة العينات لتسجيل الجلسات عند حدوث خطأ
    replaysOnErrorSampleRate: 1.0,

    // التكاملات (Integrations)
    integrations: [
      // تكامل مع React Router v6 لتتبع أداء التوجيه
      Sentry.reactRouterV6BrowserTracingIntegration({
        useEffect: useEffect, // استخدام useEffect من React
      }),

      // تكامل إعادة تشغيل الجلسة لتسجيل تفاعلات المستخدم
      Sentry.replayIntegration({
        maskAllText: false, // عدم إخفاء جميع النصوص في التسجيل
        blockAllMedia: false, // عدم حظر جميع الوسائط في التسجيل
      }),
    ],

    // تصفية الأخطاء قبل إرسالها إلى Sentry
    beforeSend(event, hint) {
      const errorMessage = event.exception?.values?.[0]?.value || '';
      const errorType = event.exception?.values?.[0]?.type || '';

      // تصفية بعض الأخطاء في وضع التطوير
      if (import.meta.env.MODE === 'development') {
        // عدم إرسال أخطاء الشبكة في وضع التطوير
        if (errorType === 'NetworkError') {
          return null;
        }

        // عدم إرسال أخطاء CORS في وضع التطوير
        if (errorMessage.includes('CORS') || errorMessage.includes('Cross-Origin')) {
          return null;
        }
      }

      // تصفية أخطاء امتدادات المتصفح الشائعة
      if (errorMessage.includes('chrome-extension://') ||
          errorMessage.includes('moz-extension://') ||
          errorMessage.includes('safari-extension://')) {
        return null;
      }

      // تصفية أخطاء تحميل السكريبت من أدوات حظر الإعلانات أو مشاكل التحميل
      if (errorMessage.includes('Script error') ||
          errorMessage.includes('Loading chunk') ||
          errorMessage.includes('Loading CSS chunk')) {
        return null;
      }

      // تصفية أخطاء الطرف الثالث الشائعة
      if (errorMessage.includes('Non-Error promise rejection captured') ||
          errorMessage.includes('ResizeObserver loop limit exceeded') ||
          errorMessage.includes('AbortError')) {
        return null;
      }

      // تصفية الأخطاء المتعلقة بالمصادقة المتوقعة
      if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
        // يتم التقاطها فقط إذا لم تكن فشل مصادقة متوقعًا من AuthContext
        if (event.tags?.component === 'AuthContext' && event.tags?.operation === 'validateToken') {
          return null;
        }
      }

      // تصفية أخطاء رد الاتصال المتوقعة للمصادقة التي تظهر للمستخدم
      if (errorMessage.includes('Auth callback error: server_error') &&
          event.tags?.component === 'AuthCallbackPage') {
        // هذه الأخطاء متوقعة عندما يواجه المستخدمون مشاكل في المصادقة - لا ترسلها إلى Sentry
        return null;
      }

      // تصفية أخطاء تعارض المنافذ في وضع التطوير
      if (import.meta.env.MODE === 'development' &&
          errorMessage.includes('EADDRINUSE')) {
        return null;
      }

      // تصفية الأخطاء ذات القيمة المنخفضة في وضع التطوير
      if (errorMessage.includes('Failed to fetch') &&
          import.meta.env.MODE === 'development') {
        return null;
      }

      return event; // إرسال الحدث إلى Sentry إذا لم يتم تصفيته
    },

    // تتبع إصدار التطبيق
    release: import.meta.env.VITE_APP_VERSION || '1.0.0',

    // تفعيل وضع التصحيح (debug mode) في وضع التطوير
    debug: import.meta.env.MODE === 'development',
  });

  // تسجيل رسالة في وحدة التحكم عند تهيئة Sentry في وضع التطوير
  if (import.meta.env.MODE === 'development') {
    console.log(`تم تهيئة Sentry لبيئة ${import.meta.env.MODE}`);
  }
};

/**
 * التقاط خطأ مع سياق إضافي.
 * تسمح هذه الدالة بإرسال الأخطاء إلى Sentry مع معلومات إضافية مفيدة.
 * @param {Error} error - الخطأ المراد التقاطه.
 * @param {object} [options] - خيارات إضافية.
 * @param {object} [options.context={}] - معلومات سياقية إضافية (مثل المكون، العملية).
 * @param {object} [options.user=null] - معلومات المستخدم المرتبطة بالخطأ.
 */
export const captureErrorWithContext = (error, { context = {}, user = null } = {}) => {
  Sentry.withScope((scope) => {
    // إضافة سياق المستخدم إذا تم توفيره
    if (user) {
      scope.setUser({
        id: user._id || user.id,
        email: user.email,
        username: user.username,
        role: user.role,
      });
    }

    // إضافة سياق مخصص إذا تم توفيره
    if (Object.keys(context).length > 0) {
      scope.setContext('custom', context);
    }

    // إضافة علامات (tags) لتحسين التصفية والبحث في Sentry
    scope.setTag('component', context.component || 'frontend');
    scope.setTag('operation', context.operation || 'unknown');

    // التقاط الخطأ
    Sentry.captureException(error);
  });
};

/**
 * التقاط رسالة مع سياق إضافي.
 * تستخدم لإرسال رسائل غير خطأ إلى Sentry لأغراض المراقبة أو التصحيح.
 * @param {string} message - الرسالة المراد التقاطها.
 * @param {string} [level='info'] - مستوى الخطورة (مثل 'info', 'warning', 'error', 'fatal').
 * @param {object} [context={}] - معلومات سياقية إضافية.
 */
export const captureMessageWithContext = (message, level = 'info', context = {}) => {
  Sentry.withScope((scope) => {
    if (Object.keys(context).length > 0) {
      scope.setContext('custom', context);
    }

    Sentry.captureMessage(message, level);
  });
};

/**
 * تعيين سياق المستخدم للنطاق الحالي في Sentry.
 * @param {object} user - معلومات المستخدم (مثل id, email, username, role).
 */
export const setUserContext = (user) => {
  Sentry.setUser({
    id: user._id || user.id,
    email: user.email,
    username: user.username,
    role: user.role,
  });
};

/**
 * مسح سياق المستخدم من Sentry.
 */
export const clearUserContext = () => {
  Sentry.setUser(null);
};

/**
 * إنشاء موجه React (React Router) مغلف بـ Sentry.
 * يستخدم لتتبع أداء التوجيه في Sentry.
 */
export const createSentryRouter = Sentry.wrapCreateBrowserRouter;

/**
 * مكون Sentry Error Boundary.
 * يستخدم لالتقاط الأخطاء في شجرة مكونات React ومنع تعطل التطبيق بالكامل.
 */
export const SentryErrorBoundary = Sentry.ErrorBoundary;

// إعادة تصدير كائن Sentry الرئيسي لتسهيل الوصول إليه
export { Sentry };