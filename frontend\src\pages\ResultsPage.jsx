// استيراد هوكس React Router للوصول إلى حالة التنقل والتنقل البرمجي
import { useLocation, useNavigate } from 'react-router-dom';
// استيراد مكون Doughnut من react-chartjs-2 لرسم الرسم البياني الدائري
import { Doughnut } from 'react-chartjs-2';
// استيراد المكونات الأساسية من chart.js وتسجيلها
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

// تسجيل المكونات الضرورية لـ Chart.js
ChartJS.register(ArcElement, Tooltip, Legend);

/**
 * مكون ResultsPage (صفحة النتائج).
 * يعرض هذا المكون نتائج جلسة اختبار مكتملة، بما في ذلك الدرجة الإجمالية
 * وتوزيع الإجابات الصحيحة والخاطئة وغير المجابة باستخدام رسم بياني دائري.
 * 
 * @returns {JSX.Element} - صفحة عرض نتائج الاختبار.
 */
const ResultsPage = () => {
    // هوك للتنقل البرمجي بين المسارات
    const navigate = useNavigate();
    // هوك للوصول إلى كائن الموقع (location object) الذي يحتوي على حالة التنقل
    const location = useLocation();

    // استخراج بيانات النتائج والجلسة من حالة التنقل
    // يتم استخدام `|| {}` لتجنب الأخطاء إذا كانت `location.state` فارغة
    const { results, session } = location.state || {};

    // معالجة حالة عدم وجود بيانات النتائج
    if (!results || !session) {
        return (
            <div className="dashboard-container">
                <div className="card text-center">
                    <h3 className="text-error mb-md">لا توجد بيانات نتائج</h3>
                    <p className="mb-lg">لم يتم العثور على نتائج اختبار. يرجى إجراء اختبار أولاً.</p>
                    <button onClick={() => navigate('/dashboard')} className="btn btn-primary">
                        العودة إلى لوحة التحكم
                    </button>
                </div>
            </div>
        );
    }

    // استخراج بيانات النتائج الرئيسية
    const {
        score,
        correctAnswers,
        wrongAnswers,
        unansweredAnswers,
        totalQuestions
    } = results;

    // إعداد بيانات الرسم البياني الدائري
    const chartData = {
        labels: ['صحيح', 'خطأ', 'لم يتم الإجابة'], // تسميات الأقسام
        datasets: [{
            data: [correctAnswers, wrongAnswers, unansweredAnswers], // البيانات العددية لكل قسم
            backgroundColor: ['#00C853', '#D50000', '#F59E0B'], // ألوان الخلفية للأقسام (أخضر، أحمر، برتقالي)
            borderColor: '#000000', // لون حدود الأقسام
            borderWidth: 2, // سمك حدود الأقسام
        }]
    };

    // إعداد خيارات الرسم البياني الدائري
    const chartOptions = {
        responsive: true, // جعل الرسم البياني متجاوبًا مع حجم الشاشة
        maintainAspectRatio: false, // عدم الحفاظ على نسبة العرض إلى الارتفاع
        cutout: '70%', // حجم الفتحة في منتصف الرسم البياني (لإنشاء شكل الدونات)
        plugins: {
            legend: { display: false }, // إخفاء وسيلة الإيضاح الافتراضية
            tooltip: { // تخصيص تلميحات الأدوات (tooltips)
                backgroundColor: '#111111', // لون خلفية التلميح
                titleColor: '#FFFFFF', // لون عنوان التلميح
                bodyColor: '#FFFFFF', // لون نص التلميح
                borderColor: '#6C5CE7', // لون حدود التلميح
                borderWidth: 1, // سمك حدود التلميح
                callbacks: {
                    // دالة لتخصيص نص التلميح
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        // حساب النسبة المئوية لكل قسم
                        const percentage = ((value / totalQuestions) * 100).toFixed(1);
                        return `${label}: ${value} (${percentage}%)`;
                    }
                }
            }
        }
    };

    return (
        <div className="results-container">
            <h2 className="text-center mb-xl">نتائج الاختبار</h2>

            {/* ملخص الدرجة والرسم البياني */}
            <div className="score-summary">
                {/* حاوية الرسم البياني الدائري مع الدرجة المئوية في المنتصف */}
                <div style={{ position: 'relative', width: '200px', height: '200px', margin: '0 auto 1.5rem' }}>
                    <Doughnut data={chartData} options={chartOptions} />
                    {/* عرض الدرجة المئوية في منتصف الرسم البياني */}
                    <div className="score-display" style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        // تغيير لون الدرجة بناءً على قيمتها
                        color: score >= 70 ? '#00C853' : score >= 50 ? '#F59E0B' : '#D50000'
                    }}>
                        {score}%
                    </div>
                </div>

                {/* ملخص الأداء النصي */}
                <div className="mb-lg">
                    <h3 className="mb-sm">أداؤك</h3>
                    <p className="text-secondary">
                        لقد أجبت على {correctAnswers} من أصل {totalQuestions} سؤال بشكل صحيح
                    </p>
                </div>

                {/* تفاصيل توزيع الدرجات (صحيح، خطأ، لم يتم الإجابة) */}
                <div className="score-breakdown">
                    <div className="score-item correct">
                        <div className="score-item-value">{correctAnswers}</div>
                        <div>صحيح</div>
                    </div>
                    <div className="score-item wrong">
                        <div className="score-item-value">{wrongAnswers}</div>
                        <div>خطأ</div>
                    </div>
                    <div className="score-item unanswered">
                        <div className="score-item-value">{unansweredAnswers}</div>
                        <div>لم يتم الإجابة</div>
                    </div>
                </div>
            </div>

            {/* رسالة الدرجة الكاملة (تظهر فقط إذا كانت جميع الإجابات صحيحة) */}
            {wrongAnswers === 0 && (
                <div className="success-message text-center mt-xl">
                    <h3 className="text-success mb-sm">درجة كاملة! 🎉</h3>
                    <p>عمل ممتاز! لقد أجبت على جميع الأسئلة بشكل صحيح.</p>
                </div>
            )}

            {/* أزرار الإجراءات */}
            <div className="text-center mt-xl">
                <button onClick={() => navigate('/dashboard')} className="btn btn-primary mr-md">
                    العودة إلى لوحة التحكم
                </button>
                <button onClick={() => navigate('/dashboard')} className="btn btn-secondary">
                    إجراء اختبار آخر
                </button>
            </div>
        </div>
    );
};

// تصدير المكون ليكون متاحًا للاستخدام في أجزاء أخرى من التطبيق
export default ResultsPage;
