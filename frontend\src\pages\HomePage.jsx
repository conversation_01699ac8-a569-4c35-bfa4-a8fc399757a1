// استيراد هوك useAuth للوصول إلى بيانات المصادقة
import { useAuth } from '../context/AuthContext';
// استيراد مكونات React Router للتنقل وإعادة التوجيه
import { Link, Navigate } from 'react-router-dom';

/**
 * مكون HomePage (الصفحة الرئيسية).
 * هذه الصفحة هي نقطة الدخول الرئيسية للتطبيق للمستخدمين غير المسجلين.
 * تعرض معلومات حول المنصة وتشجع المستخدمين على تسجيل الدخول.
 * إذا كان المستخدم مسجلاً للدخول بالفعل، يتم إعادة توجيهه تلقائيًا إلى لوحة التحكم.
 * 
 * @returns {JSX.Element} - الصفحة الرئيسية أو مكون إعادة التوجيه.
 */
const HomePage = () => {
    // الحصول على بيانات المستخدم من سياق المصادقة
    const { user } = useAuth();

    // إذا كان المستخدم مسجلاً للدخول، يتم إعادة توجيهه مباشرة إلى لوحة التحكم.
    // استخدام "replace" يمنع المستخدم من العودة إلى هذه الصفحة باستخدام زر "الخلف" في المتصفح.
    if (user) {
        return <Navigate to="/dashboard" replace />;
    }

    // إذا لم يكن المستخدم مسجلاً للدخول، يتم عرض الصفحة الرئيسية العادية.
    return (
        <div className="homepage-container">
            {/* قسم البطل (Hero Section): الجزء العلوي الجذاب من الصفحة */}
            <section className="hero-section">
                <div className="hero-content">
                    <div className="hero-icon">🎓</div>
                    <h1 className="hero-title">منصة اختبارات تمرة</h1>
                    <p className="hero-subtitle">
                        أتقن مهاراتك من خلال منصة الاختبارات الشاملة التي تتميز بـ
                        <span className="highlight"> التشبيهات اللفظية</span>،
                        <span className="highlight"> إكمال الجمل</span>،
                        <span className="highlight"> الأخطاء السياقية</span>،
                        <span className="highlight"> الشاذ</span>، و
                        <span className="highlight"> أسئلة فهم المقروء</span>.
                    </p>
                    {/* قسم الدعوة إلى الإجراء (Call to Action) */}
                    <div className="hero-cta">
                        <div className="cta-card">
                            <h3 className="cta-title">هل أنت مستعد للبدء؟</h3>
                            <p className="cta-description">
                                انقر على زر "تسجيل الدخول" في شريط التنقل أعلاه لتسجيل الدخول باستخدام تليجرام
                                والوصول إلى تجربة الاختبار المخصصة لك.
                            </p>
                            <div className="cta-pointer">👆</div>
                            <p className="cta-hint">
                                ابحث عن زر تسجيل الدخول في شريط التنقل العلوي
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* قسم الميزات (Features Section): يسلط الضوء على فوائد المنصة */}
            <section className="features-section">
                <div className="features-header">
                    <h2 className="features-title">لماذا تختار منصتنا؟</h2>
                    <p className="features-subtitle">اكتشف المزايا التي تجعل منصة الاختبارات لدينا متميزة</p>
                </div>
                <div className="features-grid">
                    {/* بطاقة الميزة: تحليلات شاملة */}
                    <div className="feature-card">
                        <div className="feature-icon">📊</div>
                        <h4 className="feature-title">تحليلات شاملة</h4>
                        <p className="feature-description">
                            تتبع تقدمك من خلال تحليلات الأداء المفصلة والرؤى الشخصية
                            لتحديد نقاط القوة ومجالات التحسين.
                        </p>
                    </div>

                    {/* بطاقة الميزة: اختبار تكيفي */}
                    <div className="feature-card">
                        <div className="feature-icon">🎯</div>
                        <h4 className="feature-title">اختبار تكيفي</h4>
                        <p className="feature-description">
                            تتكيف الأسئلة مع مستوى مهارتك لتحقيق أقصى قدر من التعلم والتحدي،
                            مما يضمن تقدمك دائمًا بالسرعة المناسبة.
                        </p>
                    </div>

                    {/* بطاقة الميزة: نتائج فورية */}
                    <div className="feature-card">
                        <div className="feature-icon">⚡</div>
                        <h4 className="feature-title">نتائج فورية</h4>
                        <p className="feature-description">
                            احصل على تغذية راجعة فورية مع شروحات مفصلة لكل سؤال،
                            مما يساعدك على التعلم من الأخطاء وتعزيز الإجابات الصحيحة.
                        </p>
                    </div>
                </div>
            </section>
        </div>
    );
};

// تصدير المكون ليكون متاحًا للاستخدام في مسارات التطبيق
export default HomePage;
