// استيراد هوكس React اللازمة
import { useEffect, useRef, useState } from 'react';
// استيراد دالة تسجيل الأخطاء إلى Sentry
import { captureErrorWithContext } from '../config/sentry';

/**
 * مكون TelegramLogin (تسجيل الدخول عبر تليجرام).
 * يتعامل هذا المكون مع عملية مصادقة OAuth الخاصة بتليجرام.
 * يقوم بتحميل أداة تليجرام البرمجية (widget) ديناميكيًا ويستخدم تدفق إعادة التوجيه من جانب الخادم
 * عبر خاصية `data-auth-url`.
 * 
 * @param {object} props - خصائص المكون.
 * @param {string} [props.size='large'] - حجم أداة تليجرام (مثل 'small', 'medium', 'large').
 * @returns {JSX.Element} - عنصر واجهة المستخدم لزر تسجيل الدخول عبر تليجرام.
 */
const TelegramLogin = ({ size = 'large' }) => {
  // مرجع لعنصر DOM الذي سيستضيف أداة تليجرام
  const telegramContainerRef = useRef(null);
  // حالة التحميل: true أثناء تحميل الأداة
  const [isLoading, setIsLoading] = useState(true);
  // حالة الخطأ: تحتوي على رسالة الخطأ إذا حدث خطأ
  const [error, setError] = useState(null);
  // حالة لتتبع ما إذا كان السكريبت قد تم تحميله بنجاح
  const [scriptLoaded, setScriptLoaded] = useState(false);

  // تأثير جانبي (useEffect) لتحميل سكريبت أداة تليجرام مرة واحدة فقط عند تحميل المكون
  useEffect(() => {
    let script = null; // متغير لتخزين مرجع السكريبت
    let timeoutId = null; // معرف المؤقت الزمني
    let mounted = true; // لتتبع ما إذا كان المكون لا يزال موجودًا في DOM

    /**
     * دالة لتحميل أداة تليجرام البرمجية.
     * تقوم بإنشاء عنصر السكريبت وإعداده وإلحاقه بـ DOM.
     */
    const loadTelegramWidget = () => {
      if (!mounted) return; // إذا تم إلغاء تحميل المكون، لا تفعل شيئًا

      setIsLoading(true); // بدء التحميل
      setError(null); // مسح أي أخطاء سابقة

      // مسح أي محتوى موجود داخل حاوية الأداة
      if (telegramContainerRef.current) {
        telegramContainerRef.current.innerHTML = '';
      }

      // إنشاء عنصر السكريبت
      script = document.createElement('script');
      // تحديد مصدر السكريبت مع إضافة معلمة زمنية لمنع التخزين المؤقت
      script.src = `https://telegram.org/js/telegram-widget.js?22&t=${Date.now()}&v=2`;
      script.async = true; // تحميل السكريبت بشكل غير متزامن
      // تعيين اسم المستخدم الخاص بالبوت لتليجرام
      script.setAttribute('data-telegram-login', 'S2wkQpfhbot');
      // تعيين حجم الأداة
      script.setAttribute('data-size', size);
      // تحديد عنوان URL لإعادة التوجيه بعد المصادقة (نقطة نهاية الواجهة الأمامية)
      const frontendUrl = window.location.origin; // الحصول على أصل عنوان URL الحالي للواجهة الأمامية
      const authUrl = `${frontendUrl}/api/users/auth/telegram/callback`; // بناء عنوان URL للرد
      script.setAttribute('data-auth-url', authUrl);
      // طلب صلاحية الكتابة (اختياري)
      script.setAttribute('data-request-access', 'write');

      // تسجيل عنوان URL للمصادقة لأغراض التصحيح
      console.log('Telegram widget auth URL:', authUrl);

      // معالج حدث عند تحميل السكريبت بنجاح
      script.onload = () => {
        if (!mounted) return;
        setScriptLoaded(true); // تحديث حالة تحميل السكريبت
        setIsLoading(false); // إيقاف حالة التحميل
        clearTimeout(timeoutId); // مسح المؤقت الزمني
      };

      // معالج حدث عند فشل تحميل السكريبت
      script.onerror = () => {
        if (!mounted) return;
        const errorMsg = 'فشل تحميل أداة تسجيل الدخول عبر تليجرام. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
        setError(errorMsg); // تعيين رسالة الخطأ
        setIsLoading(false); // إيقاف حالة التحميل
        setScriptLoaded(false); // تحديث حالة تحميل السكريبت
        clearTimeout(timeoutId); // مسح المؤقت الزمني

        // تسجيل الخطأ في Sentry
        captureErrorWithContext(new Error('Telegram widget script load failed'), {
          context: {
            component: 'TelegramLogin',
            operation: 'loadTelegramWidget',
          },
        });
      };

      // تعيين مؤقت زمني لفشل تحميل السكريبت
      timeoutId = setTimeout(() => {
        if (!mounted) return;
        const errorMsg = 'تستغرق أداة تليجرام وقتًا طويلاً للتحميل. يرجى تحديث الصفحة.';
        setError(errorMsg); // تعيين رسالة الخطأ
        setIsLoading(false); // إيقاف حالة التحميل

        // تسجيل خطأ المهلة في Sentry
        captureErrorWithContext(new Error('Telegram widget load timeout'), {
          context: {
            component: 'TelegramLogin',
            operation: 'loadTelegramWidget',
            timeout: '10000ms',
          },
        });
      }, 10000); // مهلة 10 ثوانٍ

      // إلحاق السكريبت بحاوية الأداة في DOM
      if (telegramContainerRef.current) {
        telegramContainerRef.current.appendChild(script);
      } else {
        setError('لم يتم العثور على حاوية الأداة. يرجى تحديث الصفحة.');
        setIsLoading(false);
      }
    };

    // تحميل الأداة بعد تأخير قصير لضمان تهيئة DOM
    const initTimeout = setTimeout(loadTelegramWidget, 500);

    // دالة التنظيف (cleanup) التي يتم تشغيلها عند إلغاء تحميل المكون
    return () => {
      mounted = false; // تحديث حالة التثبيت
      clearTimeout(initTimeout); // مسح مؤقت التهيئة
      clearTimeout(timeoutId); // مسح مؤقت التحميل
      // إزالة عنصر السكريبت من DOM إذا كان موجودًا
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []); // مصفوفة التبعيات فارغة، مما يعني أن هذا التأثير يعمل مرة واحدة فقط عند تحميل المكون

  /**
   * دالة لمعالجة إعادة المحاولة.
   * تقوم ببساطة بإعادة تحميل الصفحة لإعادة تشغيل الأداة.
   */
  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <div className="telegram-login-container">
      {/* حالة التحميل: تظهر عندما يكون isLoading صحيحًا ولا يوجد خطأ */}
      {isLoading && !error && (
        <div className="loading">
          <div className="spinner"></div>
          <span>جاري تحميل تسجيل الدخول عبر تليجرام...</span>
        </div>
      )}

      {/* حالة الخطأ: تظهر عندما يكون هناك خطأ */}
      {error && (
        <div className="error-message">
          <div className="text-center mb-md">⚠️</div>
          <p className="text-center mb-md">{error}</p>
          <button onClick={handleRetry} className="btn btn-secondary">
            حاول مرة أخرى
          </button>
        </div>
      )}

      {/* حاوية أداة تليجرام: يتم إخفاؤها أثناء التحميل أو وجود خطأ */}
      <div
        ref={telegramContainerRef}
        className="telegram-widget-container"
        style={{
          display: (isLoading || error) ? 'none' : 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          minHeight: '60px'
        }}
      />
    </div>
  );
};

// تصدير المكون ليكون متاحًا للاستخدام في أجزاء أخرى من التطبيق
export default TelegramLogin;