# مكونات الواجهة الأمامية (Frontend Components)

يحتوي هذا المجلد على المكونات الأساسية والقابلة لإعادة الاستخدام التي تشكل واجهة المستخدم للتطبيق. تم تصميم هذه المكونات لتكون معيارية وواضحة الوظيفة، مما يسهل عملية التطوير والصيانة.

## هيكل المجلد

يتكون المجلد من ملفات `JSX` التي تمثل مكونات React، بالإضافة إلى ملفات `CSS` التي توفر التنسيقات الخاصة ببعض هذه المكونات.

## قائمة المكونات والملفات

فيما يلي وصف لكل ملف ومكون داخل هذا المجلد:

### `AdminRoute.jsx`
مكون حماية المسار (Route Guard) يضمن أن المسارات المحمية لا يمكن الوصول إليها إلا من قبل المستخدمين الذين يمتلكون صلاحيات "admin" أو "sudo". يقوم بإعادة توجيه المستخدمين غير المصرح لهم إلى صفحات أخرى.

### `BookmarkButton.jsx`
مكون زر الإشارة المرجعية. يوفر واجهة مستخدم تفاعلية لإضافة أو إزالة الأسئلة من قائمة الإشارات المرجعية. يتميز بتصميم جذاب وحركات CSS عند التفاعل.

### `LogoutButton.jsx`
مكون زر تسجيل الخروج. يوفر زرًا بسيطًا وواضحًا للمستخدمين لتسجيل الخروج من حساباتهم. يستدعي دالة تسجيل الخروج المقدمة كخاصية (prop).

### `Navbar.css`
ملف التنسيقات (CSS) الذي يوفر الأنماط البصرية لشريط التنقل الرئيسي (`Navbar.jsx`)، وزر تسجيل الخروج (`LogoutButton.jsx`)، ومفتاح تبديل المظهر (`ThemeSwitcher.jsx`). يحتوي على تعريفات للخطوط، الألوان، التخطيط، والحركات.

### `Navbar.jsx`
مكون شريط التنقل الرئيسي للتطبيق. يعرض شعار التطبيق، ويوفر روابط التنقل، ويحتوي على زر تسجيل الدخول/الخروج (حسب حالة المستخدم)، ومفتاح تبديل المظهر.

### `ProtectedRoute.jsx`
مكون حماية المسار (Route Guard) يضمن أن المسارات المحمية لا يمكن الوصول إليها إلا من قبل المستخدمين الذين قاموا بتسجيل الدخول. يقوم بإعادة توجيه المستخدمين غير المصادق عليهم إلى الصفحة الرئيسية.

### `SingleQuestionPage.css`
ملف التنسيقات (CSS) الخاص بصفحة عرض السؤال الواحد. يوفر الأنماط لبطاقة السؤال، وخيارات الإجابة، وتأثيرات التفاعل، بالإضافة إلى التنسيقات الخاصة بالوضع الداكن والتصميم المتجاوب.

### `SingleQuestionPage.jsx`
مكون لعرض سؤال واحد مع خيارات الإجابة. يسمح للمستخدم باختيار إجابة واحدة ويعرض تغذية راجعة بصرية (صحيح/خطأ) بعد الإجابة.

### `TelegramLogin.jsx`
مكون زر تسجيل الدخول عبر تليجرام. يقوم بتحميل أداة تليجرام البرمجية ديناميكيًا ويتعامل مع عملية المصادقة عبر OAuth. يتضمن منطقًا للتحميل، معالجة الأخطاء، وإعادة المحاولة.

### `ThemeSwitcher.jsx`
مكون مفتاح تبديل المظهر. يوفر واجهة مستخدم بصرية لتبديل مظهر التطبيق بين الوضع الفاتح والداكن. يعتمد على تنسيقات CSS خارجية لوظيفته البصرية.
