/* Tamruh Design System - Dual Theme Support */

/* Google Fonts Import for Arabic Typography */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

/* Light Theme Variables (Default) */
:root {
  --primary-color: #6C5CE7;
  --secondary-color: #A8A5E6;
  --background: #F8F9FA; /* لون خلفية أفتح قليلاً */
  --card-bg: #FFFFFF;
  --text-color: #2D3748;
  --border-color: #E2E8F0;
  --shadow-color: rgba(0, 0, 0, 0.05);

  /* Theme transition */
  --theme-transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark Theme Variables */
.theme-dark {
  --primary-color: #6C5CE7;
  --secondary-color: #A8A5E6;
  --background: #121212;
  --card-bg: #1E1E1E;
  --text-color: #E2E8F0;
  --border-color: #2D3748;
  --shadow-color: rgba(0, 0, 0, 0.2);
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Tajawal', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background);
  color: var(--text-color);
  min-height: 100vh;
  font-size: 16px;
  line-height: 1.6;
  transition: var(--theme-transition);
}

#root {
  min-height: 100vh;
}

/* Dashboard Styles */
.dashboard-modern {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background-color: var(--background);
  color: var(--text-color);
  transition: var(--theme-transition);
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 2rem;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--card-bg);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px var(--shadow-color);
  border: 1px solid var(--border-color);
  transition: var(--theme-transition);
}

.welcome-text {
  flex: 1;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.dashboard-subtitle {
  font-size: 1.125rem;
  color: var(--text-color);
  opacity: 0.8;
  line-height: 1.5;
}

.user-avatar {
  margin-left: 2rem;
}

.avatar-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 2rem;
}

.action-card {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 16px var(--shadow-color);
  border: 1px solid var(--border-color);
  transition: var(--theme-transition);
}

.primary-action {
  text-align: center;
}

/* Modern Button Styles */
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.125rem;
  font-weight: 600;
  font-family: 'Tajawal', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-height: 56px;
}

.btn-primary-modern {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
}

.btn-primary-modern:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(108, 92, 231, 0.4);
}

.btn-primary-modern:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.start-test-btn-modern {
  font-size: 1.25rem;
  padding: 1.25rem 2.5rem;
}

.btn-icon {
  font-size: 1.5rem;
}

.btn-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  background: #fee2e2;
  border: 1px solid #ef4444;
  color: #991b1b;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.theme-dark .error-message {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
  color: #fca5a5;
}

.mb-lg {
  margin-bottom: 2rem;
}

/* Stats Section */
.stats-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px var(--shadow-color);
  border: 1px solid var(--border-color);
  transition: var(--theme-transition);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-color);
}

.stat-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 12px;
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  line-height: 1;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color);
  opacity: 0.7;
  font-weight: 500;
}

/* Loading and No Data States */
.stats-loading,
.stats-no-data {
  text-align: center;
  padding: 2rem;
  background: var(--card-bg);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-modern {
    padding: 1rem;
  }

  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .user-avatar {
    margin-left: 0;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-subtitle {
    font-size: 1rem;
  }

  .avatar-circle {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .btn-modern {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .start-test-btn-modern {
    font-size: 1.125rem;
    padding: 1rem 2rem;
  }
}