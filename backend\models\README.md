# نماذج الواجهة الخلفية (Backend Models)

يحتوي هذا المجلد على تعريفات نماذج البيانات (Mongoose Schemas and Models) التي تمثل بنية المستندات المخزنة في قاعدة بيانات MongoDB. هذه النماذج تحدد شكل البيانات، أنواع الحقول، العلاقات بين المستندات، والتحقق من صحة البيانات.

## هيكل المجلد

يتكون المجلد من ملفات JavaScript، حيث يختص كل ملف بتعريف نموذج لكيان بيانات معين في التطبيق.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `Question.js`
يحدد هذا الملف المخططات والنماذج لأنواع مختلفة من الأسئلة (مثل أسئلة الإكمال، اكتشاف الخطأ، التشبيه، فهم المقروء، الشاذ). يستخدم ميزة المُميِّزات (Discriminators) في Mongoose لتخزين أنواع أسئلة متعددة في مجموعة واحدة مع الحفاظ على حقول مشتركة وحقول خاصة لكل نوع.

### `TestSession.js`
يحدد هذا الملف المخطط والنموذج لجلسة اختبار واحدة. يتتبع المستخدم الذي أجرى الاختبار، الأسئلة التي تم تضمينها في الجلسة، إجابات المستخدم، حالة الجلسة (قيد التقدم أو مكتملة)، الدرجة، وتواريخ البدء والانتهاء.

### `User.js`
يحدد هذا الملف المخطط والنموذج لمستخدم في التطبيق. يتضمن حقولاً لـ `telegramId` (المعرف الأساسي للمستخدم)، الاسم الكامل، اسم المستخدم، البريد الإلكتروني (اختياري)، الدور (مثل 'admin', 'user'), مستوى الأداء، وتفضيلات المستخدم. تم تصميمه خصيصًا للمصادقة عبر Telegram.
