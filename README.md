# منصة اختبارات تمرة (Tumrah Testing Platform)

مرحبًا بك في منصة اختبارات تمرة! هذا المشروع هو تطبيق ويب كامل (Full-stack) مصمم لمساعدة المستخدمين على إتقان مهاراتهم من خلال أنواع مختلفة من الأسئلة، بما في ذلك التشبيهات اللفظية، إكمال الجمل، اكتشاف الأخطاء، الشاذ، وأسئلة فهم المقروء. يوفر التطبيق تجربة اختبار تفاعلية وغامرة، ولوحة تحكم شخصية، وإحصائيات أداء مفصلة، بالإضافة إلى أدوات إدارة الأسئلة للمديرين.

يهدف هذا التوثيق إلى توفير فهم شامل للمشروع، من هيكله العام إلى تفاصيل كل مكون ووظيفة، مما يجعله سهل الفهم لأي مطور جديد ينضم إلى الفريق أو لأي شخص يرغب في استكشاف بنية التطبيق.

## نظرة عامة على المشروع

يتكون المشروع من جزأين رئيسيين يعملان بشكل متكامل:

1.  **الواجهة الأمامية (Frontend)**: تطبيق ويب تفاعلي مبني باستخدام مكتبة React.js وإطار عمل Vite.js. مسؤول عن تقديم واجهة المستخدم، التفاعل مع المستخدمين، وعرض البيانات المستلمة من الواجهة الخلفية.
2.  **الواجهة الخلفية (Backend)**: خادم API مبني باستخدام Node.js وإطار عمل Express.js، ويتفاعل مع قاعدة بيانات MongoDB. مسؤول عن منطق الأعمال، إدارة البيانات، المصادقة، وتوفير نقاط النهاية (API endpoints) للواجهة الأمامية.

## هيكل المشروع

يتبع المشروع هيكل مجلدات واضحًا ومنظمًا، مما يسهل التنقل والفهم:

```
tumrah/
├── backend/                  # مجلد الواجهة الخلفية (Node.js/Express.js API)
│   ├── config/               # إعدادات وتكوينات الخادم (مثل التسجيل، Sentry)
│   │   ├── logger.js         # إعدادات نظام التسجيل (Winston)
│   │   └── sentry.js         # تهيئة Sentry لمراقبة الأخطاء في الواجهة الخلفية
│   ├── controllers/          # دوال التحكم التي تحتوي على منطق الأعمال لكل مسار API
│   │   ├── questionController.js # معالجة عمليات CRUD للأسئلة
│   │   ├── testController.js # إدارة جلسات الاختبار وحساب النتائج
│   │   └── userController.js # مصادقة المستخدمين وإدارة ملفاتهم الشخصية
│   ├── middleware/           # دوال وسيطة لمعالجة الطلبات والاستجابات
│   │   ├── authMiddleware.js # حماية المسارات بالمصادقة والترخيص (JWT)
│   │   ├── errorMiddleware.js # معالج الأخطاء المركزي (404، 500، Sentry، Winston)
│   │   └── validationMiddleware.js # معالجة أخطاء التحقق من صحة البيانات (express-validator)
│   ├── models/               # تعريفات نماذج البيانات (Mongoose Schemas and Models)
│   │   ├── Question.js       # نماذج لأنواع الأسئلة المختلفة باستخدام Discriminators
│   │   ├── TestSession.js    # نموذج لتتبع جلسات الاختبار
│   │   └── User.js           # نموذج بيانات المستخدم (مصادقة Telegram)
│   ├── routes/               # تعريفات مسارات API وتوجيهها إلى المتحكمات
│   │   ├── questionRoutes.js # مسارات API للأسئلة
│   │   ├── testRoutes.js     # مسارات API لجلسات الاختبار
│   │   └── userRoutes.js     # مسارات API للمستخدمين والمصادقة
│   ├── tests/                # اختبارات الوحدة والتكامل للواجهة الخلفية (Jest, Supertest)
│   │   ├── sentry.test.js    # اختبارات تكامل Sentry
│   │   ├── server.test.js    # اختبارات نقاط النهاية الأساسية للخادم
│   │   └── setup.js          # إعداد بيئة الاختبار (MongoDB في الذاكرة)
│   ├── validators/           # قواعد التحقق من صحة البيانات الواردة (express-validator)
│   │   └── userValidator.js  # قواعد التحقق لبيانات المستخدم (مصادقة وتحديث الملف الشخصي)
│   ├── .env                  # متغيرات البيئة الخاصة بالواجهة الخلفية (لا يتم تتبعها في Git)
│   ├── app.js                # إعداد تطبيق Express الرئيسي وتكوين middleware
│   ├── server.js             # نقطة الدخول لتشغيل الخادم والاتصال بقاعدة البيانات
│   └── swaggerConfig.js      # إعدادات Swagger/OpenAPI لتوثيق API
├── frontend/                 # مجلد الواجهة الأمامية (React.js/Vite.js Application)
│   ├── public/               # أصول ثابتة يتم تقديمها مباشرة (HTML الأساسي، أيقونات)
│   │   ├── telegram-test.html # ملف HTML لاختبار تكامل Telegram
│   │   └── vite.svg          # أيقونة Vite الافتراضية
│   ├── src/                  # الكود المصدري الرئيسي لتطبيق React
│   │   ├── App.css           # تنسيقات CSS العامة للتطبيق
│   │   ├── App.jsx           # المكون الرئيسي للتطبيق (تخطيط المسارات)
│   │   ├── index.css         # تنسيقات CSS أساسية
│   │   ├── main.jsx          # نقطة دخول تطبيق React (تهيئة Sentry، سياقات)
│   │   ├── assets/           # أصول ثابتة مثل الصور والأيقونات
│   │   │   └── react.svg     # أيقونة React
│   │   ├── components/       # مكونات React قابلة لإعادة الاستخدام
│   │   │   ├── AdminRoute.jsx # مكون لحماية المسارات الخاصة بالمدير
│   │   │   ├── BookmarkButton.jsx # زر الإشارة المرجعية للأسئلة
│   │   │   ├── LogoutButton.jsx # زر تسجيل الخروج
│   │   │   ├── Navbar.css    # تنسيقات CSS لشريط التنقل ومكونات أخرى
│   │   │   ├── Navbar.jsx    # مكون شريط التنقل الرئيسي
│   │   │   ├── ProtectedRoute.jsx # مكون لحماية المسارات التي تتطلب مصادقة
│   │   │   ├── SingleQuestionPage.css # تنسيقات لصفحة عرض السؤال الواحد
│   │   │   ├── SingleQuestionPage.jsx # مكون لعرض سؤال واحد وخياراته
│   │   │   ├── TelegramLogin.jsx # مكون زر تسجيل الدخول عبر Telegram
│   │   │   └── ThemeSwitcher.jsx # مكون تبديل المظهر (فاتح/داكن)
│   │   ├── config/           # ملفات الإعدادات والتكوينات الخاصة بالواجهة الأمامية
│   │   │   └── sentry.js     # تهيئة Sentry لمراقبة الأخطاء في الواجهة الأمامية
│   │   ├── context/          # سياقات React لإدارة الحالة العامة للتطبيق
│   │   │   ├── AuthContext.jsx # سياق المصادقة (إدارة حالة المستخدم)
│   │   │   ├── TestContext.jsx # سياق إدارة جلسات الاختبار
│   │   │   └── ThemeContext.jsx # سياق إدارة مظهر التطبيق
│   │   ├── pages/            # المكونات الرئيسية التي تمثل صفحات التطبيق
│   │   │   ├── AdminQuestionsPage.jsx # صفحة إدارة الأسئلة للمديرين
│   │   │   ├── AuthCallbackPage.jsx # معالج رد نداء مصادقة Telegram
│   │   │   ├── DashboardPage.jsx # لوحة تحكم المستخدم
│   │   │   ├── HomePage.jsx  # الصفحة الرئيسية للمستخدمين غير المسجلين
│   │   │   ├── ResultsPage.jsx # صفحة عرض نتائج الاختبار
│   │   │   ├── TelegramCallbackProxy.jsx # وكيل إعادة توجيه لرد نداء Telegram
│   │   │   ├── TestingPage.css # تنسيقات لصفحة الاختبار
│   │   │   └── TestingPage.jsx # مكون صفحة الاختبار الرئيسية
│   │   └── utils/            # دوال مساعدة وأدوات عامة
│   │       └── api.js        # غلاف لطلبات API مع معالجة الأخطاء وتكامل Sentry
│   ├── node_modules/         # تبعيات الواجهة الأمامية
│   └── ...                   # ملفات إعدادات Vite و React (مثل vite.config.js, package.json)
├── node_modules/             # تبعيات المشروع المشتركة (إذا كانت موجودة)
├── package.json              # تعريف المشروع والتبعيات الرئيسية
├── package-lock.json         # قفل تبعيات المشروع
├── README.md                 # هذا الملف (التوثيق الشامل للمشروع)
├── SENTRY_INTEGRATION.md     # وثيقة تفصيلية لتكامل Sentry (للمرجع، تم دمج محتواها هنا)
└── ...                       # ملفات سجلات أو إعدادات أخرى (مثل .gitignore, combined.log, error.log)
```

## التقنيات المستخدمة

تم بناء هذا المشروع باستخدام مجموعة من التقنيات الحديثة والقوية لضمان الأداء، قابلية التوسع، وسهولة الصيانة:

### الواجهة الأمامية (Frontend)
*   **React.js**: مكتبة JavaScript رائدة لبناء واجهات المستخدم التفاعلية والقابلة لإعادة الاستخدام.
*   **Vite.js**: أداة بناء سريعة جدًا توفر تجربة تطوير محسّنة (HMR، تجميع سريع).
*   **React Router**: لإدارة التوجيه والتنقل بين الصفحات والمكونات داخل التطبيق أحادي الصفحة (SPA).
*   **Context API**: آلية React لإدارة الحالة العامة للتطبيق ومشاركتها بين المكونات دون الحاجة إلى تمرير الخصائص يدويًا.
*   **Chart.js**: مكتبة JavaScript مرنة لعرض الرسوم البيانية، تُستخدم هنا لتصور نتائج الاختبار.
*   **Styled Components**: مكتبة لتنسيق مكونات React باستخدام CSS-in-JS، مما يوفر تنسيقات محددة النطاق وقابلة لإعادة الاستخدام.

### الواجهة الخلفية (Backend)
*   **Node.js**: بيئة تشغيل JavaScript من جانب الخادم، تتيح بناء تطبيقات شبكة قابلة للتوسع.
*   **Express.js**: إطار عمل ويب سريع وغير مقيد لـ Node.js، يستخدم لبناء واجهات برمجة التطبيقات (APIs).
*   **MongoDB**: قاعدة بيانات NoSQL موجهة بالمستندات، توفر مرونة عالية في تخزين البيانات.
*   **Mongoose**: مكتبة لنمذجة كائنات MongoDB لـ Node.js، تبسط التفاعل مع قاعدة البيانات وتوفر التحقق من صحة المخطط.
*   **JWT (JSON Web Tokens)**: معيار مفتوح لإنشاء توكنات وصول آمنة للمصادقة والترخيص.
*   **Winston**: مكتبة تسجيل قوية ومرنة لـ Node.js، تستخدم لإدارة سجلات التطبيق.
*   **Swagger/OpenAPI**: مجموعة أدوات لتوثيق واجهات برمجة التطبيقات (APIs) بشكل تفاعلي، مما يسهل فهم واستخدام API.
*   **Jest**: إطار عمل اختبار JavaScript شائع، يستخدم لاختبار الوحدة والتكامل للواجهة الخلفية.
*   **Supertest**: مكتبة تساعد في اختبار طلبات HTTP، تُستخدم مع Jest لاختبار مسارات API.
*   **express-validator**: middleware للتحقق من صحة البيانات الواردة في طلبات Express.js.
*   **express-rate-limit**: middleware لتحديد معدل الطلبات، يستخدم لحماية نقاط النهاية من هجمات القوة الغاشمة (brute-force).

### مشتركة (Common)
*   **Sentry**: منصة لمراقبة الأخطاء والأداء في الوقت الفعلي، مدمجة في كل من الواجهة الأمامية والخلفية لتوفير رؤية شاملة لصحة التطبيق.

## كيفية التشغيل

للتشغيل المحلي للمشروع بالكامل (الواجهة الأمامية والخلفية)، اتبع الخطوات التالية بدقة:

1.  **استنساخ المستودع (Clone the repository):**
    ابدأ باستنساخ كود المشروع من المستودع الخاص به:
    ```bash
    git clone <URL_المستودع_الخاص_بك_هنا>
    cd tumrah
    ```

2.  **إعداد متغيرات البيئة:**
    قم بإنشاء ملف `.env` في المجلد الجذر للمشروع (`tumrah/`). هذا الملف سيحتوي على جميع متغيرات البيئة الضرورية لكل من الواجهة الأمامية والخلفية. تأكد من استبدال القيم بين `< >` بالقيم الفعلية الخاصة بك.

    ```env
    # -------------------------------------------------------------------
    # متغيرات البيئة العامة (تستخدمها الواجهة الأمامية والخلفية)
    # -------------------------------------------------------------------
    NODE_ENV=development # بيئة التشغيل: 'development' للتطوير، 'production' للإنتاج
    PORT=5000            # المنفذ الذي ستعمل عليه الواجهة الخلفية (الخادم)
    FRONTEND_URL=http://localhost:5173 # عنوان URL للواجهة الأمامية (مهم لإعادة التوجيه بعد مصادقة Telegram)

    # -------------------------------------------------------------------
    # متغيرات البيئة الخاصة بالواجهة الخلفية (Backend)
    # -------------------------------------------------------------------
    MONGODB_URI=mongodb://localhost:27017/tumrah_db # URI الاتصال بقاعدة بيانات MongoDB
                                                  # مثال: mongodb+srv://<user>:<password>@cluster.mongodb.net/tumrah_db
    JWT_SECRET=your_super_secret_jwt_key_that_is_long_and_random # مفتاح سري لتوقيع توكنات JWT (يجب أن يكون قويًا جدًا)
    TELEGRAM_BOT_TOKEN=your_telegram_bot_token_from_botfather # توكن البوت الخاص بك من BotFather في Telegram (للمصادقة)

    # -------------------------------------------------------------------
    # إعدادات Sentry (اختيارية - لمراقبة الأخطاء والأداء)
    # -------------------------------------------------------------------
    # DSN الخاص بمشروع Sentry للواجهة الخلفية
    SENTRY_DSN=https://<EMAIL>/exampleprojectid_backend
    # DSN الخاص بمشروع Sentry للواجهة الأمامية (يجب أن يكون هو نفسه أو مختلفًا حسب إعدادات Sentry)
    VITE_SENTRY_DSN=https://<EMAIL>/exampleprojectid_frontend
    # إصدار التطبيق (يستخدم في Sentry لتتبع الإصدارات)
    VITE_APP_VERSION=1.0.0
    ```
    **ملاحظة هامة**: لا تقم بمشاركة ملف `.env` هذا في المستودعات العامة (Git repositories) لأنه يحتوي على معلومات حساسة.

3.  **تثبيت التبعيات:**
    انتقل إلى كل من مجلد `backend` ومجلد `frontend` بشكل منفصل وقم بتثبيت جميع التبعيات المطلوبة لكل جزء:

    ```bash
    # في المجلد الجذر للمشروع (tumrah/)
    cd backend
    npm install # أو yarn install
    
    cd ../frontend
    npm install # أو yarn install
    
    cd .. # العودة إلى المجلد الجذر للمشروع
    ```

4.  **تشغيل التطبيق:**
    للتشغيل الكامل للتطبيق، ستحتاج إلى تشغيل الواجهة الخلفية والواجهة الأمامية في طرفيتين منفصلتين:

    ```bash
    # -------------------------------------------------------------------
    # تشغيل الواجهة الخلفية (في طرفية جديدة)
    # -------------------------------------------------------------------
    cd backend
    npm start # هذا الأمر سيقوم بتشغيل الخادم باستخدام `node server.js`
    # أو يمكنك تشغيله مباشرة:
    # node server.js
    
    # بعد التشغيل الناجح، ستظهر رسالة مثل:
    # Connected to MongoDB Atlas
    # Backend server running in development mode on port 5000
    ```
    ```bash
    # -------------------------------------------------------------------
    # تشغيل الواجهة الأمامية (في طرفية جديدة أخرى)
    # -------------------------------------------------------------------
    cd frontend
    npm run dev # هذا الأمر سيقوم بتشغيل خادم تطوير Vite
    # أو يمكنك تشغيله مباشرة:
    # vite
    
    # بعد التشغيل الناجح، ستظهر رسالة مثل:
    # VITE vX.Y.Z  ready in Xms
    # ➜  Local:   http://localhost:5173/
    # ➜  Network: use --host to expose
    ```
    بعد التشغيل، ستكون الواجهة الأمامية متاحة عادةً على `http://localhost:5173` والواجهة الخلفية على `http://localhost:5000`.

## الوظائف الرئيسية

يقدم تطبيق "Tumrah Testing Platform" مجموعة غنية من الميزات لمساعدة المستخدمين على التعلم والتقييم:

*   **مصادقة المستخدم الآمنة**: تسجيل الدخول السهل والآمن عبر Telegram، مع التحقق من صحة البيانات لضمان الأمان.
*   **لوحة تحكم المستخدم الشخصية**: تعرض إحصائيات الأداء الرئيسية للمستخدم (مثل عدد الاختبارات المكتملة، متوسط الدرجة، أفضل درجة)، وتوفر نقطة انطلاق سريعة لبدء اختبارات جديدة.
*   **تجربة اختبار تفاعلية وغامرة**:
    *   **أنواع أسئلة متنوعة**: يدعم التطبيق أنواعًا متعددة من الأسئلة مثل التشبيهات اللفظية، إكمال الجمل، اكتشاف الأخطاء، الشاذ، وأسئلة فهم المقروء.
    *   **التنقل المرن**: يسمح بالتنقل بين الأسئلة الرئيسية والأسئلة الفرعية (لفهم المقروء).
    *   **حفظ التقدم**: يتم حفظ إجابات المستخدم تلقائيًا أثناء جلسة الاختبار.
    *   **وضع التركيز وملء الشاشة**: لتجربة اختبار خالية من التشتت.
*   **صفحة نتائج الاختبار التفصيلية**: تعرض تحليلًا شاملاً لأداء المستخدم في الاختبار، بما في ذلك الدرجة، وتوزيع الإجابات الصحيحة/الخاطئة/غير المجابة، مع تصورات بيانية (باستخدام Chart.js).
*   **إدارة الأسئلة للمديرين**: واجهة إدارية محمية (تتطلب صلاحيات 'admin' أو 'sudo') لإضافة، تعديل، وحذف الأسئلة في قاعدة البيانات.
*   **تبديل المظهر**: القدرة على التبديل بين الوضع الفاتح والداكن لواجهة المستخدم لتناسب تفضيلات المستخدم.

## مراقبة الأخطاء والأداء (Sentry Integration)

تم دمج Sentry بشكل شامل في كل من الواجهة الخلفية والواجهة الأمامية لتوفير مراقبة شاملة للأخطاء، وتتبع الأداء، وقدرات التصحيح في الوقت الفعلي.

### التكامل في الواجهة الخلفية (Node.js/Express.js)
*   **الموقع**: `backend/config/sentry.js`
*   **الميزات الرئيسية**:ل
    *   **التقاط الأخطاء التلقائي**: يلتقط الاستثناءات غير المعالجة ورفض الوعود (unhandled promise rejections).
    *   **مراقبة الأداء**: تتبع أداء طلبات HTTP، استعلامات قاعدة البيانات (MongoDB/Mongoose)، و middleware الخاص بـ Express.
    *   **إثراء السياق**: يتم التقاط معلومات المستخدم (ID، البريد الإلكتروني، الدور) وتفاصيل الطلب (الطريقة، URL، الرؤوس، الجسم) تلقائيًا.
    *   **تصفية الأخطاء الذكية**: يتم تصفية أخطاء التحقق من الصحة، أخطاء فحص الصحة، ويتم إزالة البيانات الحساسة (مثل كلمات المرور والتوكنات) تلقائيًا من تقارير الأخطاء لتقليل الضوضاء وضمان الخصوصية.

### التكامل في الواجهة الأمامية (React.js)
*   **الموقع**: `frontend/src/config/sentry.js`
*   **الميزات الرئيسية**:ل
    *   **حدود الأخطاء في React (Error Boundaries)**: تلتقط الأخطاء داخل شجرة مكونات React وتوفر واجهة مستخدم احتياطية لطيفة.
    *   **مراقبة الأداء**: تتبع أداء تغييرات المسار (React Router) وطلبات API.
    *   **إعادة تشغيل الجلسة (Session Replay)**: تسجل تفاعلات المستخدم (بشكل يحافظ على الخصوصية) مما يساعد في إعادة إنتاج الأخطاء.
    *   **تتبع سياق المستخدم**: يتم ربط الأخطاء تلقائيًا بالمستخدم الحالي من سياق المصادقة.

### أفضل الممارسات في معالجة الأخطاء
*   **كتل `try-catch` الشاملة**: يتم استخدامها بشكل استراتيجي في منطق الأعمال الحرج لالتقاط الأخطاء وإرسالها إلى Sentry مع سياق إضافي.
*   **استجابات الأخطاء اللطيفة**: يتم توفير رسائل خطأ سهلة الاستخدام للعميل، بينما يتم تسجيل التفاصيل الكاملة للخطأ في Sentry لأغراض التصحيح.
*   **التقاط الأخطاء الغني بالسياق**: يتم تضمين معلومات سياقية ذات صلة (مثل المكون الذي حدث فيه الخطأ، العملية، بيانات المستخدم، تفاصيل الطلب) مع كل خطأ يتم التقاطه، مما يسرع عملية تحديد المشكلة وحلها.

### الاختبار
للتأكد من أن تكامل Sentry يعمل بشكل صحيح، يمكنك تشغيل اختبارات التكامل المخصصة:
```bash
# في المجلد الجذر للمشروع
cd backend
npm test -- sentry.test.js
```
تتحقق هذه الاختبارات من وظائف التقاط الأخطاء والرسائل، وتكامل middleware معالجة الأخطاء، ومعالجة أخطاء API، ومرونة التهيئة.

### المراقبة والتنبيهات
يمكنك الوصول إلى لوحة تحكم Sentry الخاصة بك (الرابط متوفر في إعدادات Sentry) لمراقبة المقاييس الرئيسية مثل معدل الأخطاء، وقت الاستجابة، تأثير المستخدم، واتجاهات الأخطاء. يوصى بإعداد تنبيهات مخصصة لمعدلات الأخطاء العالية، وأنواع الأخطاء الجديدة، وتدهور الأداء، أو عندما تؤثر الأخطاء على عدد كبير من المستخدمين.

## اعتبارات الإنتاج

*   **معدلات أخذ العينات**: في بيئة الإنتاج، يتم تكوين Sentry لاستيعاب 100% من الأخطاء، ولكن يتم أخذ عينات من تتبع الأداء والتنميط (عادةً 10%) لتقليل الحمل على الخادم وشبكة Sentry.
*   **خصوصية البيانات**: يتم تصفية البيانات الحساسة (مثل كلمات المرور والتوكنات) تلقائيًا من تقارير Sentry. يمكن تكوين تسجيل الجلسات (Session Replay) ليكون أكثر وعيًا بالخصوصية إذا لزم الأمر.
*   **تأثير الأداء**: تأثير Sentry على أداء التطبيق ضئيل في الإنتاج بفضل آليات الإبلاغ غير المتزامنة ومعدلات أخذ العينات القابلة للتكوين.

## المساهمة

نرحب بالمساهمات في هذا المشروع! يرجى قراءة ملف `CONTRIBUTING.md` (إذا كان موجودًا) للحصول على إرشادات حول كيفية إعداد بيئة التطوير، تشغيل الاختبارات، وتقديم المساهمات.

---