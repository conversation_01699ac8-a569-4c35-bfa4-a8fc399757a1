// استيراد هوكس React لإدارة دورة حياة المكونات
import { useEffect, useState } from 'react';
// استيراد هوكس React Router للتنقل واستخراج معلمات البحث من URL
import { useNavigate, useSearchParams } from 'react-router-dom';
// استيراد هوك useAuth للوصول إلى دالة تسجيل الدخول من سياق المصادقة
import { useAuth } from '../context/AuthContext';
// استيراد دالة Sentry لالتقاط الأخطاء مع السياق
import { captureErrorWithContext } from '../config/sentry';

/**
 * مكون AuthCallbackPage (صفحة رد نداء المصادقة).
 * هذا المكون مسؤول عن معالجة رد الاتصال من مزود المصادقة (مثل تليجرام).
 * لا يعرض أي واجهة مستخدم مباشرة، بل يقوم بمعالجة التوكن أو رسالة الخطأ
 * من عنوان URL ثم يقوم بتسجيل دخول المستخدم أو إعادة توجيهه.
 * 
 * @returns {null} - لا يعرض هذا المكون أي شيء في الواجهة.
 */
const AuthCallbackPage = () => {
  // هوك لاستخراج معلمات البحث (query parameters) من عنوان URL
  const [searchParams] = useSearchParams();
  // هوك للتنقل البرمجي بين المسارات
  const navigate = useNavigate();
  // الحصول على دالة تسجيل الدخول من سياق المصادقة
  const { login } = useAuth();

  // تأثير جانبي (useEffect) لمعالجة رد نداء المصادقة عند تحميل المكون
  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // استخراج التوكن ورسالة الخطأ من معلمات البحث في عنوان URL
        const token = searchParams.get('token');
        const errorParam = searchParams.get('error');

        // إذا كان هناك معلمة خطأ في عنوان URL
        if (errorParam) {
          // تسجيل الخطأ في وحدة التحكم و Sentry
          console.error(`خطأ في رد نداء المصادقة: ${errorParam}`);
          captureErrorWithContext(new Error(`Auth callback error: ${errorParam}`));
          // إعادة التوجيه فورًا إلى الصفحة الرئيسية
          navigate('/', { replace: true });
          return;
        }

        // إذا لم يتم استلام توكن مصادقة
        if (!token) {
          throw new Error('لم يتم استلام توكن مصادقة.');
        }

        // بناء عنوان URL لنقطة نهاية ملف تعريف المستخدم
        const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || window.location.origin;
        const profileUrl = `${apiBaseUrl}/api/users/profile`;

        // إرسال طلب لجلب ملف تعريف المستخدم باستخدام التوكن المستلم
        const response = await fetch(profileUrl, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        // التحقق مما إذا كان الاستجابة ناجحة
        if (!response.ok) {
          throw new Error('فشل التحقق من صحة التوكن.');
        }

        // تحليل بيانات المستخدم من الاستجابة
        const userData = await response.json();
        // دمج بيانات المستخدم مع التوكن
        const userWithToken = { ...userData, token };
        // محاولة تسجيل دخول المستخدم باستخدام دالة login من سياق المصادقة
        const loginResult = await login(userWithToken);

        // إذا كان تسجيل الدخول ناجحًا، يتم التوجيه إلى لوحة التحكم
        if (loginResult.success) {
          navigate('/dashboard', { replace: true });
        } else {
          // إذا فشل تسجيل الدخول، يتم رمي خطأ
          throw new Error(loginResult.error || 'فشل تسجيل الدخول');
        }

      } catch (err) {
        // معالجة أي أخطاء تحدث أثناء عملية رد نداء المصادقة
        console.error('خطأ في رد نداء المصادقة:', err);
        captureErrorWithContext(err); // تسجيل الخطأ في Sentry
        navigate('/', { replace: true }); // العودة للصفحة الرئيسية عند حدوث أي خطأ
      }
    };

    handleAuthCallback(); // استدعاء دالة معالجة رد نداء المصادقة عند تحميل المكون
  }, [searchParams, login, navigate]); // يعتمد على هذه المتغيرات لإعادة تشغيل التأثير

  // هذا المكون لا يعرض أي واجهة مستخدم، بل يقوم بالمنطق في الخلفية.
  return null;
};

// تصدير المكون ليكون متاحًا للاستخدام في مسارات التطبيق
export default AuthCallbackPage;
