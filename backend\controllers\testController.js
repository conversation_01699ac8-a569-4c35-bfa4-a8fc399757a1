// استيراد `express-async-handler` لتبسيط معالجة الأخطاء في الدوال غير المتزامنة
const asyncHandler = require('express-async-handler');
// استيراد نماذج Mongoose
const User = require('../models/User'); // نموذج المستخدم
const TestSession = require('../models/TestSession'); // نموذج جلسة الاختبار
const { Question } = require('../models/Question'); // نموذج السؤال الأساسي

/**
 * دالة `startTest`.
 * تبدأ جلسة اختبار جديدة للمستخدم.
 * تقوم بجلب عدد محدد من الأسئلة العشوائية من قاعدة البيانات،
 * وتنشئ جلسة اختبار جديدة في قاعدة البيانات، ثم تعيد الأسئلة ومعرف الجلسة.
 * 
 * @param {object} req - كائن الطلب (يحتوي على `questionCount` في `req.body` ومعرف المستخدم في `req.user._id`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل معرف الجلسة والأسئلة الكاملة.
 * @throws {Error} - ترمي خطأ إذا لم تكن هناك أسئلة كافية.
 */
const startTest = asyncHandler(async (req, res) => {
    // تحديد عدد الأسئلة المطلوبة للاختبار، الافتراضي هو 10
    const questionCount = req.body.questionCount ? Number(req.body.questionCount) : 10;
    
    // 1. جلب أسئلة عشوائية من قاعدة البيانات باستخدام `aggregate` و `$sample`
    const randomQuestions = await Question.aggregate([
        { $sample: { size: questionCount } } // اختيار عدد عشوائي من الأسئلة
    ]);

    // التحقق مما إذا تم جلب أي أسئلة
    if (!randomQuestions || randomQuestions.length === 0) {
        res.status(404); // تعيين حالة HTTP إلى 404 (غير موجود)
        throw new Error('لا توجد أسئلة كافية في قاعدة البيانات لبدء اختبار.');
    }

    // 2. استخراج معرفات الأسئلة التي تم جلبها لإنشاء جلسة الاختبار
    const questionIds = randomQuestions.map(q => q._id);

    // 3. إنشاء جلسة اختبار جديدة في قاعدة البيانات
    const newSession = await TestSession.create({
        user: req.user._id, // ربط الجلسة بالمستخدم الحالي
        questions: questionIds, // حفظ معرفات الأسئلة في الجلسة
    });

    // 4. إرسال معرف الجلسة والأسئلة الكاملة (بما في ذلك تفاصيلها) إلى الواجهة الأمامية
    res.status(201).json({
        sessionId: newSession._id,
        questions: randomQuestions,
    });
});

/**
 * دالة `getTestSession`.ل
 * جلب جلسة اختبار نشطة بواسطة معرف الجلسة.
 * 
 * @param {object} req - كائن الطلب (يحتوي على `sessionId` في `req.params` ومعرف المستخدم في `req.user._id`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل كائن جلسة الاختبار مع تفاصيل الأسئلة.
 * @throws {Error} - ترمي خطأ إذا لم يتم العثور على الجلسة أو لا تخص المستخدم.
 */
const getTestSession = asyncHandler(async (req, res) => {
    const { sessionId } = req.params; // استخراج معرف الجلسة من معلمات المسار

    // البحث عن جلسة الاختبار بواسطة المعرف ومعرف المستخدم
    // استخدام `.populate('questions')` لجلب تفاصيل كائنات الأسئلة المرتبطة بالجلسة
    const session = await TestSession.findOne({
        _id: sessionId,
        user: req.user._id,
    }).populate('questions');

    // التحقق مما إذا تم العثور على الجلسة
    if (!session) {
        res.status(404);
        throw new Error('جلسة الاختبار هذه غير موجودة أو لا تخصك.');
    }

    res.status(200).json(session); // إرسال كائن الجلسة مع حالة 200
});

/**
 * دالة `saveAnswer`.ل
 * حفظ إجابة المستخدم لسؤال معين ضمن جلسة اختبار.
 * 
 * @param {object} req - كائن الطلب (يحتوي على `sessionId` في `req.params` و `questionId`, `answer` في `req.body`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل رسالة نجاح.
 * @throws {Error} - ترمي خطأ إذا لم يتم العثور على الجلسة أو كانت مكتملة.
 */
const saveAnswer = asyncHandler(async (req, res) => {
    const { sessionId } = req.params; // استخراج معرف الجلسة
    const { questionId, answer } = req.body; // استخراج معرف السؤال والإجابة
    // البحث عن جلسة الاختبار
    const testSession = await TestSession.findOne({ _id: sessionId, user: req.user._id });
    if (!testSession) {
        res.status(404);
        throw new Error('جلسة الاختبار غير موجودة أو لا تخص هذا المستخدم.');
    }
    // منع حفظ الإجابات إذا كانت الجلسة مكتملة
    if (testSession.status === 'completed') {
        res.status(400);
        throw new Error('لا يمكن تعديل الإجابات بعد إكمال الاختبار.');
    }
    // حفظ الإجابة في خريطة `userAnswers` الخاصة بالجلسة
    testSession.userAnswers.set(questionId, answer);
    await testSession.save(); // حفظ التغييرات في قاعدة البيانات
    res.status(200).json({
        message: 'تم حفظ الإجابة بنجاح.',
    });
});

/**
 * دالة `submitTestSession`.ل
 * تسليم جلسة اختبار وحساب النتائج النهائية.
 * 
 * @param {object} req - كائن الطلب (يحتوي على `sessionId` في `req.params` ومعرف المستخدم في `req.user._id`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل النتائج التفصيلية للاختبار.
 * @throws {Error} - ترمي خطأ إذا لم يتم العثور على الجلسة أو كانت مكتملة بالفعل.
 */
const submitTestSession = asyncHandler(async (req, res) => {
    const { sessionId } = req.params; // استخراج معرف الجلسة

    // البحث عن جلسة الاختبار وجلب تفاصيل الأسئلة المرتبطة بها
    const session = await TestSession.findOne({
        _id: sessionId,
        user: req.user._id,
    }).populate('questions');

    // التحقق مما إذا تم العثور على الجلسة
    if (!session) {
        res.status(404);
        throw new Error('جلسة الاختبار غير موجودة أو لا تخصك.');
    }

    // التحقق مما إذا كان الاختبار قد تم تسليمه مسبقًا
    if (session.status === 'completed') {
        res.status(400);
        throw new Error('تم تسليم هذا الاختبار مسبقًا.');
    }

    // حساب النتائج
    let correctCount = 0; // عدد الإجابات الصحيحة
    let unansweredCount = 0; // عدد الأسئلة غير المجابة
    const totalQuestions = session.questions.length; // العدد الكلي للأسئلة
    const detailedResults = []; // مصفوفة لتخزين النتائج التفصيلية لكل سؤال

    // المرور على كل سؤال في الجلسة لحساب النتائج
    for (const question of session.questions) {
        // الحصول على إجابة المستخدم لهذا السؤال
        const userAnswer = session.userAnswers.get(question._id.toString());
        let isCorrect = false; // هل الإجابة صحيحة؟
        let isAnswered = userAnswer !== undefined && userAnswer !== null; // هل تم الإجابة على السؤال؟

        if (!isAnswered) {
            unansweredCount++; // زيادة عداد الأسئلة غير المجابة
        } else {
            // معالجة أنواع الأسئلة المختلفة لتحديد الإجابة الصحيحة
            if (question.type === 'reading') {
                // بالنسبة لأسئلة فهم المقروء، يتم التحقق من الإجابة الصحيحة للسؤال الفرعي الأول
                // (قد تحتاج هذه الجزئية إلى منطق أكثر تعقيدًا إذا كان هناك عدة أسئلة فرعية)
                isCorrect = userAnswer === question.questions[0]?.correct_option;
            } else {
                // للأنواع الأخرى، يتم التحقق من الإجابة الصحيحة للسؤال الرئيسي
                isCorrect = userAnswer === question.correct_option;
            }

            if (isCorrect) {
                correctCount++; // زيادة عداد الإجابات الصحيحة
            }
        }

        // إضافة النتائج التفصيلية لهذا السؤال
        detailedResults.push({
            questionId: question._id,
            userAnswer: userAnswer,
            correctAnswer: question.correct_option || question.questions?.[0]?.correct_option, // الإجابة الصحيحة
            isCorrect: isCorrect,
            isAnswered: isAnswered
        });

        // تحديث إحصائيات السؤال في قاعدة البيانات (عدد المحاولات والإجابات الصحيحة)
        await Question.updateOne(
            { _id: question._id },
            {
                $inc: { // زيادة القيم
                    'stats.attempts': 1,
                    'stats.correct': isCorrect ? 1 : 0
                }
            }
        );
    }

    const wrongCount = totalQuestions - correctCount - unansweredCount; // حساب عدد الإجابات الخاطئة
    // حساب الدرجة المئوية (تقريب لأقرب عدد صحيح)
    const score = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 100) : 0;

    // تحديث حالة الجلسة والدرجة في قاعدة البيانات
    session.status = 'completed'; // تعيين حالة الجلسة إلى مكتملة
    session.score = score; // حفظ الدرجة
    session.completedAt = new Date(); // تسجيل وقت الانتهاء
    await session.save(); // حفظ التغييرات

    // تحديث مستوى المستخدم بناءً على الدرجة
    const userToUpdate = await User.findById(req.user._id);
    if (userToUpdate) {
        userToUpdate.level = score; // تعيين مستوى المستخدم إلى الدرجة
        await userToUpdate.save(); // حفظ التغييرات
    }

    // إرسال النتائج النهائية إلى الواجهة الأمامية
    res.status(200).json({
        message: 'تم تسليم الاختبار بنجاح!',
        score: score,
        correctAnswers: correctCount,
        wrongAnswers: wrongCount,
        unansweredAnswers: unansweredCount,
        totalQuestions: totalQuestions,
        detailedResults: detailedResults,
        sessionId: session._id
    });
});

/**
 * دالة `submitTest`.ل
 * (ملاحظة: هذه الدالة قد تكون قديمة أو مكررة مع `submitTestSession`،
 * يفضل استخدام `submitTestSession` لإدارة جلسات الاختبار بشكل كامل).
 * تقوم بحساب نتيجة الاختبار وتحديث مستوى المستخدم.
 * 
 * @param {object} req - كائن الطلب (يحتوي على `quizData` و `userAnswers` في `req.body`).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل رسالة نجاح والدرجة.
 * @throws {Error} - ترمي خطأ إذا كانت بيانات الاختبار غير مكتملة أو المستخدم غير موجود.
 */
const submitTest = asyncHandler(async (req, res) => {
    const { quizData, userAnswers } = req.body; // استخراج بيانات الاختبار وإجابات المستخدم
    if (!quizData || !userAnswers) {
        res.status(400);
        throw new Error('بيانات الاختبار غير مكتملة');
    }
    let correctCount = 0;
    const totalQuestions = quizData.length;
    // المرور على كل سؤال لحساب الإجابات الصحيحة
    for (let i = 0; i < quizData.length; i++) {
        const question = quizData[i];
        const userAnswer = userAnswers[i];
        const isCorrect = userAnswer === question.correct_option;
        if (isCorrect) {
            correctCount++;
        }
        // تحديث إحصائيات السؤال
        await Question.updateOne(
            { _id: question._id },
            { $inc: { 'stats.attempts': 1, 'stats.correct': isCorrect ? 1 : 0 } }
        );
    }
    const score = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 100) : 0;
    // تحديث مستوى المستخدم
    const userToUpdate = await User.findById(req.user._id);
    if (userToUpdate) {
        userToUpdate.level = score;
        await userToUpdate.save();
    } else {
        res.status(404);
        throw new Error('المستخدم غير موجود');
    }
    res.status(200).json({
        message: 'تم تسجيل نتيجتك بنجاح!',
        score: score,
        correctAnswers: correctCount,
        totalQuestions: totalQuestions,
    });
});

// تصدير جميع دوال التحكم في الاختبارات
module.exports = {
    startTest,
    getTestSession,
    submitTestSession,
    saveAnswer,
    submitTest, // تصدير الدالة القديمة أيضًا (للتوافق إذا كانت لا تزال مستخدمة)
};