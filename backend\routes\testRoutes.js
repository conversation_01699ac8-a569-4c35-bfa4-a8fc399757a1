const express = require('express');
const { startTest, getTestSession, submitTestSession, submitTest, saveAnswer } = require('../controllers/testController');
const { protect } = require('../middleware/authMiddleware');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   - name: Tests
 *     description: عمليات الاختبار وتسليم النتائج
 */

/**
 * @swagger
 * /api/tests/start:
 *   post:
 *     summary: بدء جلسة اختبار جديدة
 *     description: يقوم بإنشاء جلسة اختبار جديدة للمستخدم الحالي مع مجموعة من الأسئلة ويعيد معرّف الجلسة.
 *     tags: [Tests]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "201":
 *         description: تم إنشاء الجلسة بنجاح.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 sessionId:
 *                   type: string
 *                   description: معرّف الجلسة الذي سيتم استخدامه في بقية عمليات الاختبار.
 *       "401":
 *         description: المستخدم غير مصادق عليه.
 *       "404":
 *         description: لا توجد أسئلة متاحة.
 */
// @route   POST /api/tests/start
// @desc    بدء جلسة اختبار جديدة
// @access  خاص (Private) - يتطلب مصادقة
router.post('/start', protect, startTest);

/**
 * @swagger
 * /api/tests/session/{sessionId}:
 *   get:
 *     summary: جلب بيانات جلسة اختبار حالية
 *     description: يسترجع جميع تفاصيل جلسة اختبار معينة، بما في ذلك قائمة الأسئلة الكاملة والإجابات المحفوظة.
 *     tags: [Tests]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: معرّف جلسة الاختبار
 *     responses:
 *       "200":
 *         description: تم جلب بيانات الجلسة بنجاح.
 *       "401":
 *         description: المستخدم غير مصادق عليه.
 *       "404":
 *         description: جلسة الاختبار غير موجودة.
 */
// @route   GET /api/tests/session/:sessionId
// @desc    جلب بيانات جلسة اختبار حالية
// @access  خاص (Private) - يتطلب مصادقة
router.get('/session/:sessionId', protect, getTestSession);

/**
 * @swagger
 * /api/tests/session/{sessionId}/submit:
 *   post:
 *     summary: تسليم جلسة اختبار وحساب النتيجة النهائية
 *     description: يقوم بإنهاء جلسة اختبار جارية، ويحسب النتيجة بناءً على الإجابات المحفوظة، ويحدث حالة الجلسة والمستخدم.
 *     tags: [Tests]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: معرّف جلسة الاختبار التي سيتم تسليمها
 *     responses:
 *       "200":
 *         description: تم تسليم الاختبار بنجاح.
 *       "400":
 *         description: تم تسليم هذا الاختبار مسبقًا.
 *       "401":
 *         description: المستخدم غير مصادق عليه.
 *       "404":
 *         description: جلسة الاختبار غير موجودة.
 */
// @route   POST /api/tests/session/:sessionId/submit
// @desc    تسليم جلسة اختبار وحساب النتيجة النهائية
// @access  خاص (Private) - يتطلب مصادقة
router.post('/session/:sessionId/submit', protect, submitTestSession);

/**
 * @swagger
 * /api/tests/session/{sessionId}/answer:
 *   put:
 *     summary: حفظ إجابة المستخدم لسؤال معين
 *     description: يحفظ إجابة المستخدم أثناء جلسة اختبار جارية، مما يسمح بحفظ التقدم.
 *     tags: [Tests]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: معرّف جلسة الاختبار
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               questionId:
 *                 type: string
 *                 description: معرّف السؤال الذي يتم الإجابة عليه
 *               answer:
 *                 type: string
 *                 description: مفتاح الإجابة التي اختارها المستخدم (e.g., 'A', 'B')
 *     responses:
 *       "200":
 *         description: تم حفظ الإجابة بنجاح.
 *       "400":
 *         description: الاختبار مكتمل بالفعل.
 *       "401":
 *         description: المستخدم غير مصادق عليه.
 *       "404":
 *         description: جلسة الاختبار غير موجودة.
 */
// @route   PUT /api/tests/session/:sessionId/answer
// @desc    حفظ إجابة المستخدم لسؤال معين
// @access  خاص (Private) - يتطلب مصادقة
router.put('/session/:sessionId/answer', protect, saveAnswer);

// تصدير كائن Router لاستخدامه في ملف التطبيق الرئيسي (app.js)
module.exports = router;
