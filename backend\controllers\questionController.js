// استيراد `express-async-handler` لتبسيط معالجة الأخطاء في الدوال غير المتزامنة
const asyncHandler = require('express-async-handler');
// استيراد نماذج (models) الأسئلة من ملف Question.js
const models = require('../models/Question');

/**
 * دالة `createQuestion`.
 * تتحكم في إنشاء سؤال جديد.
 * تحدد نوع السؤال من جسم الطلب وتستخدم النموذج المناسب لإنشاء السؤال في قاعدة البيانات.
 * 
 * @param {object} req - كائن الطلب (Request object) من Express.
 * @param {object} res - كائن الاستجابة (Response object) من Express.
 * @returns {Promise<void>} - لا تعيد قيمة مباشرة، بل ترسل استجابة HTTP.
 * @throws {Error} - ترمي خطأ إذا كان نوع السؤال مفقودًا أو غير صالح.
 */
const createQuestion = asyncHandler(async (req, res) => {
    const { type } = req.body; // استخراج نوع السؤال من جسم الطلب
    if (!type) {
        res.status(400); // تعيين حالة HTTP إلى 400 (طلب سيء)
        throw new Error('حقل "type" إلزامي لتحديد نوع السؤال.'); // رمي خطأ
    }

    let Model; // متغير لتخزين نموذج Mongoose المناسب
    // اختيار النموذج بناءً على نوع السؤال
    switch (type) {
        case 'error': Model = models.ErrorQuestion; break;
        case 'analogy': Model = models.AnalogyQuestion; break;
        case 'completion': Model = models.CompletionQuestion; break;
        case 'reading': Model = models.ReadingQuestion; break;
        case 'odd_one_out': Model = models.OddOneOutQuestion; break;
        default:
            res.status(400);
            throw new Error(`نوع السؤال غير صالح: ${type}`);
    }
    
    // إنشاء السؤال في قاعدة البيانات باستخدام النموذج المحدد
    const question = await Model.create(req.body);
    res.status(201).json(question); // إرسال السؤال الذي تم إنشاؤه مع حالة 201 (تم الإنشاء)
});

/**
 * دالة `getQuestions`.
 * تتحكم في جلب جميع الأسئلة من قاعدة البيانات.
 * 
 * @param {object} req - كائن الطلب.
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل قائمة الأسئلة مع حالة 200.
 */
const getQuestions = asyncHandler(async (req, res) => {
    // جلب جميع الأسئلة باستخدام النموذج الأساسي Question
    const questions = await models.Question.find({});
    res.status(200).json(questions); // إرسال قائمة الأسئلة مع حالة 200 (موافق)
});

/**
 * دالة `getQuestionById`.
 * تتحكم في جلب سؤال واحد بواسطة معرفه.
 * 
 * @param {object} req - كائن الطلب (يحتوي على معرف السؤال في req.params.id).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل السؤال إذا وجد، وإلا ترمي خطأ 404.
 */
const getQuestionById = asyncHandler(async (req, res) => {
    // البحث عن سؤال بواسطة المعرف
    const question = await models.Question.findById(req.params.id);
    if (question) {
        res.status(200).json(question); // إرسال السؤال مع حالة 200
    } else {
        res.status(404); // تعيين حالة HTTP إلى 404 (غير موجود)
        throw new Error('السؤال غير موجود'); // رمي خطأ
    }
});

/**
 * دالة `getQuestionsByIds`.
 * تتحكم في جلب عدة أسئلة بواسطة قائمة من المعرفات.
 * 
 * @param {object} req - كائن الطلب (يحتوي على معرفات الأسئلة في req.query.ids).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل قائمة الأسئلة مع حالة 200.
 * @throws {Error} - ترمي خطأ إذا لم يتم توفير أي معرفات.
 */
const getQuestionsByIds = asyncHandler(async (req, res) => {
    const { ids } = req.query; // استخراج المعرفات من معلمات الاستعلام
    if (!ids) {
        res.status(400);
        throw new Error('لم يتم توفير أي معرفات للأسئلة');
    }
    const idsArray = ids.split(','); // تحويل سلسلة المعرفات إلى مصفوفة
    // جلب الأسئلة التي تتطابق معرفاتها مع المعرفات في المصفوفة
    const questions = await models.Question.find({ '_id': { $in: idsArray } });
    res.status(200).json(questions); // إرسال قائمة الأسئلة مع حالة 200
});

/**
 * دالة `updateQuestion`.
 * تتحكم في تحديث سؤال موجود بواسطة معرفه.
 * 
 * @param {object} req - كائن الطلب (يحتوي على معرف السؤال في req.params.id وبيانات التحديث في req.body).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل السؤال المحدث إذا وجد، وإلا ترمي خطأ 404 أو 400.
 * @throws {Error} - ترمي خطأ إذا كان السؤال غير موجود أو حاول المستخدم تغيير نوع السؤال.
 */
const updateQuestion = asyncHandler(async (req, res) => {
    // البحث عن السؤال بواسطة المعرف
    const question = await models.Question.findById(req.params.id);
    if (!question) {
        res.status(404);
        throw new Error('السؤال غير موجود');
    }
    // منع تغيير نوع السؤال بعد إنشائه
    if (req.body.type && req.body.type !== question.type) {
        res.status(400);
        throw new Error('لا يمكن تغيير نوع السؤال بعد إنشائه.');
    }
    // تحديث السؤال في قاعدة البيانات
    const updatedQuestion = await models.Question.findByIdAndUpdate(
        req.params.id, // معرف السؤال
        req.body, // البيانات المراد تحديثها
        { new: true, runValidators: true } // إرجاع المستند المحدث وتشغيل المدققات
    );
    res.status(200).json(updatedQuestion); // إرسال السؤال المحدث مع حالة 200
});

/**
 * دالة `deleteQuestion`.
 * تتحكم في حذف سؤال موجود بواسطة معرفه.
 * 
 * @param {object} req - كائن الطلب (يحتوي على معرف السؤال في req.params.id).
 * @param {object} res - كائن الاستجابة.
 * @returns {Promise<void>} - ترسل رسالة نجاح إذا تم الحذف، وإلا ترمي خطأ 404.
 * @throws {Error} - ترمي خطأ إذا كان السؤال غير موجود.
 */
const deleteQuestion = asyncHandler(async (req, res) => {
    // البحث عن السؤال بواسطة المعرف
    const question = await models.Question.findById(req.params.id);
    if (question) {
        await question.deleteOne(); // حذف السؤال من قاعدة البيانات
        res.status(200).json({ message: 'تم حذف السؤال بنجاح' }); // إرسال رسالة نجاح مع حالة 200
    } else {
        res.status(404);
        throw new Error('السؤال غير موجود');
    }
});

// تصدير جميع دوال التحكم في الأسئلة
module.exports = {
    createQuestion,
    getQuestions,
    getQuestionById,
    getQuestionsByIds,
    updateQuestion,
    deleteQuestion
};