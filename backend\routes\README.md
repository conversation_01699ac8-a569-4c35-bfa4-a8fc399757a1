# مسارات الواجهة الخلفية (Backend Routes)

يحتوي هذا المجلد على تعريفات المسارات (routes) الخاصة بواجهة برمجة التطبيقات (API) لتطبيق Express.js. كل ملف هنا يمثل مجموعة من المسارات المتعلقة بكيان معين (مثل المستخدمين، الأسئلة، جلسات الاختبار)، ويوجه الطلبات الواردة إلى دوال التحكم (controllers) المناسبة بعد تطبيق أي وسيطات (middleware) ضرورية للمصادقة أو التحقق من الصحة.

## هيكل المجلد

يتكون المجلد من ملفات JavaScript، حيث يختص كل ملف بتعريف المسارات لنوع معين من الموارد.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `questionRoutes.js`
يحدد مسارات API لعمليات CRUD (الإنشاء، القراءة، التحديث، الحذف) المتعلقة بالأسئلة. يتضمن مسارات عامة لجلب الأسئلة ومسارات محمية تتطلب صلاحيات المدير (admin/sudo) لإنشاء، تحديث، وحذف الأسئلة.

### `testRoutes.js`
يحدد مسارات API لإدارة جلسات الاختبار. يتضمن مسارات لبدء اختبارات جديدة، جلب بيانات جلسة اختبار حالية، حفظ إجابات المستخدم، وتسليم جلسة الاختبار وحساب النتائج النهائية. جميع هذه المسارات محمية وتتطلب مصادقة المستخدم.

### `userRoutes.js`
يحدد مسارات API لعمليات المستخدمين. يتضمن مسارات لمصادقة المستخدمين عبر Telegram (بما في ذلك معالج رد النداء)، جلب وتحديث ملفات المستخدم الشخصية، وجلب إحصائيات المستخدم. يتم تطبيق تحديد معدل الطلبات (rate limiting) على مسارات المصادقة لتعزيز الأمان.
