// استيراد إطار عمل Express
const express = require('express');
// استيراد دوال التحكم (controllers) الخاصة بالأسئلة
const {
    createQuestion,
    getQuestions,
    getQuestionById,
    getQuestionsByIds,
    updateQuestion,
    deleteQuestion
} = require('../controllers/questionController');
// استيراد دوال middleware للمصادقة والترخيص
const { protect, authorizeRoles } = require('../middleware/authMiddleware');

// إنشاء كائن Router من Express
const router = express.Router();

// --- المسارات العامة (Public Routes) ---
// هذه المسارات لا تتطلب مصادقة المستخدم.

/**
 * @route GET /api/questions/
 * @desc جلب جميع الأسئلة.
 * @access عام (Public)
 */
router.get('/', getQuestions);

/**
 * @route GET /api/questions/by-ids
 * @desc جلب أسئلة محددة بواسطة قائمة من المعرفات.
 * @access عام (Public)
 * ملاحظة: يجب وضع هذا المسار قبل `/:id` لتجنب تضارب التوجيه في Express.
 */
router.get('/by-ids', getQuestionsByIds);

/**
 * @route GET /api/questions/:id
 * @desc جلب سؤال واحد بواسطة معرفه.
 * @access عام (Public)
 */
router.get('/:id', getQuestionById);

// --- المسارات المحمية (Protected Routes) ---
// هذه المسارات تتطلب مصادقة المستخدم ودورًا محددًا.

/**
 * @route POST /api/questions/
 * @desc إنشاء سؤال جديد.
 * @access خاص (Private) - يتطلب دور 'admin' أو 'sudo'.
 * @middleware protect - للتحقق من مصادقة المستخدم.
 * @middleware authorizeRoles('admin', 'sudo') - للتحقق من صلاحيات المستخدم.
 */
router.post(
    '/',
    protect, // التحقق من أن المستخدم مسجل الدخول
    authorizeRoles('admin', 'sudo'), // التحقق من أن المستخدم لديه دور 'admin' أو 'sudo'
    createQuestion // دالة التحكم لإنشاء السؤال
);

/**
 * @route PUT /api/questions/:id
 * @desc تحديث سؤال موجود بواسطة معرفه.
 * @access خاص (Private) - يتطلب دور 'admin' أو 'sudo'.
 * @middleware protect - للتحقق من مصادقة المستخدم.
 * @middleware authorizeRoles('admin', 'sudo') - للتحقق من صلاحيات المستخدم.
 */
router.put(
    '/:id',
    protect, // التحقق من أن المستخدم مسجل الدخول
    authorizeRoles('admin', 'sudo'), // التحقق من أن المستخدم لديه دور 'admin' أو 'sudo'
    updateQuestion // دالة التحكم لتحديث السؤال
);

/**
 * @route DELETE /api/questions/:id
 * @desc حذف سؤال بواسطة معرفه.
 * @access خاص (Private) - يتطلب دور 'admin' أو 'sudo'.
 * @middleware protect - للتحقق من مصادقة المستخدم.
 * @middleware authorizeRoles('admin', 'sudo') - للتحقق من صلاحيات المستخدم.
 */
router.delete(
    '/:id',
    protect, // التحقق من أن المستخدم مسجل الدخول
    authorizeRoles('admin', 'sudo'), // التحقق من أن المستخدم لديه دور 'admin' أو 'sudo'
    deleteQuestion // دالة التحكم لحذف السؤال
);

// تصدير كائن Router لاستخدامه في ملف التطبيق الرئيسي (app.js)
module.exports = router;
