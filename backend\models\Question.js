// backend/models/Question.js

// استيراد مكتبة Mongoose للتعامل مع MongoDB
const mongoose = require('mongoose');
const { Schema } = mongoose; // استخراج كائن Schema من Mongoose

/**
 * `baseQuestionSchema` (النموذج الأساسي للسؤال).
 * يحدد الحقول المشتركة لجميع أنواع الأسئلة.
 * 
 * @property {number} question_number - رقم السؤال (اختياري).
 * @property {number} difficulty_level - مستوى الصعوبة (من 1 إلى 5).
 * @property {boolean} taqfeelah - حقل منطقي (boolean) يشير إلى خاصية معينة (افتراضي: false).
 * @property {string} notes - ملاحظات إضافية حول السؤال (اختياري، يتم إزالة المسافات البيضاء الزائدة).
 * @property {string} explanation - شرح الإجابة الصحيحة للسؤال (اختياري، يتم إزالة المسافات البيضاء الزائدة).
 * 
 * @options
 *   `discriminatorKey: 'type'` - يحدد الحقل الذي سيتم استخدامه لتمييز أنواع المستندات المختلفة في نفس المجموعة.
 *   `collection: 'questions'` - يحدد أن جميع أنواع الأسئلة (بما في ذلك المشتقة) ستُخزن في مجموعة واحدة تسمى 'questions'.
 */
const baseQuestionSchema = new Schema({
    question_number: { type: Number },
    difficulty_level: { type: Number, min: 1, max: 5 },
    taqfeelah: { type: Boolean, default: false },
    notes: { type: String, trim: true },
    explanation: { type: String, trim: true },
}, {
    discriminatorKey: 'type', // الحقل الذي يميز نوع السؤال (مثل 'completion', 'error')
    collection: 'questions', // جميع الأسئلة ستُخزن في مجموعة واحدة
});

// إنشاء النموذج الأساسي للسؤال. جميع أنواع الأسئلة الأخرى ستكون مشتقة من هذا النموذج.
const Question = mongoose.model('Question', baseQuestionSchema);

/**
 * `simpleOptionSchema` (نموذج الخيار البسيط).
 * يحدد بنية خيار إجابة بسيط.
 * 
 * @property {string} text - نص الخيار (إلزامي).
 * @property {number} mqfelah - حقل رقمي (اختياري).
 * 
 * @options
 *   `_id: false` - يمنع Mongoose من إنشاء معرف `_id` فريد لكل خيار فرعي.
 */
const simpleOptionSchema = new Schema({
    text: { type: String, required: true },
    mqfelah: { type: Number }
}, { _id: false });

/**
 * `simpleChoiceSchema` (نموذج الاختيار البسيط).
 * يحدد بنية الأسئلة ذات الخيارات المتعددة البسيطة (مثل إكمال الجملة، الشاذ).
 * 
 * @property {string} question_text - نص السؤال (إلزامي، يتم إزالة المسافات البيضاء الزائدة).
 * @property {object} options - كائن يحتوي على الخيارات (A, B, C, D) باستخدام `simpleOptionSchema`.
 * @property {string} correct_option - الخيار الصحيح (إلزامي، يجب أن يكون 'A', 'B', 'C', أو 'D').
 */
const simpleChoiceSchema = new Schema({
    question_text: { type: String, required: true, trim: true },
    options: {
        A: { type: simpleOptionSchema, required: true },
        B: { type: simpleOptionSchema, required: true },
        C: { type: simpleOptionSchema, required: true },
        D: { type: simpleOptionSchema, required: true },
    },
    correct_option: { type: String, required: true, enum: ['A', 'B', 'C', 'D'] },
});

/**
 * `errorQuestionSchema` (نموذج سؤال اكتشاف الخطأ).
 * يمتد من `simpleChoiceSchema` ويضيف حقل `correction`.
 * 
 * @property {string} correction - النص الصحيح أو التصحيح للخطأ (إلزامي، يتم إزالة المسافات البيضاء الزائدة).
 */
const errorQuestionSchema = new Schema({
    question_text: { type: String, required: true, trim: true },
    options: {
        A: { type: simpleOptionSchema, required: true },
        B: { type: simpleOptionSchema, required: true },
        C: { type: simpleOptionSchema, required: true },
        D: { type: simpleOptionSchema, required: true },
    },
    correct_option: { type: String, required: true, enum: ['A', 'B', 'C', 'D'] },
    correction: { type: String, required: true, trim: true },
});

/**
 * `complexChoiceOptionSchema` (نموذج خيار الاختيار المعقد).
 * يحدد بنية خيار إجابة أكثر تعقيدًا (مثل أسئلة التشبيه).
 * 
 * @property {string} text - نص الخيار (إلزامي).
 * @property {string} relation - وصف العلاقة (اختياري).
 * @property {string} meaning - المعنى (اختياري).
 * @property {number} mqfelah - حقل رقمي (اختياري).
 * 
 * @options
 *   `_id: false` - يمنع Mongoose من إنشاء معرف `_id` فريد لكل خيار فرعي.
 */
const complexChoiceOptionSchema = new Schema({
    text: { type: String, required: true },
    relation: { type: String },
    meaning: { type: String },
    mqfelah: { type: Number }
}, { _id: false });

/**
 * `complexChoiceSchema` (نموذج الاختيار المعقد).
 * يحدد بنية الأسئلة ذات الخيارات المتعددة المعقدة (مثل التشبيه، الشاذ).
 * 
 * @property {string} question_text - نص السؤال (إلزامي، يتم إزالة المسافات البيضاء الزائدة).
 * @property {object} options - كائن يحتوي على الخيارات (A, B, C, D) باستخدام `complexChoiceOptionSchema`.
 * @property {string} correct_option - الخيار الصحيح (إلزامي، يجب أن يكون 'A', 'B', 'C', أو 'D').
 */
const complexChoiceSchema = new Schema({
    question_text: { type: String, required: true, trim: true },
    options: {
        A: complexChoiceOptionSchema,
        B: complexChoiceOptionSchema,
        C: complexChoiceOptionSchema,
        D: complexChoiceOptionSchema,
    },
    correct_option: { type: String, required: true, enum: ['A', 'B', 'C', 'D'] },
});

/**
 * `subQuestionSchema` (نموذج السؤال الفرعي).
 * يحدد بنية الأسئلة الفرعية ضمن أسئلة فهم المقروء.
 * 
 * @property {string} question_text - نص السؤال الفرعي (إلزامي، يتم إزالة المسافات البيضاء الزائدة).
 * @property {object} options - كائن يحتوي على الخيارات (A, B, C, D) باستخدام `simpleOptionSchema`.
 * @property {string} correct_option - الخيار الصحيح (إلزامي، يجب أن يكون 'A', 'B', 'C', أو 'D').
 * @property {number} difficulty_level - مستوى الصعوبة (من 1 إلى 5).
 * @property {boolean} taqfeelah - حقل منطقي (boolean) (افتراضي: false).
 * @property {string} notes - ملاحظات إضافية (اختياري).
 * @property {string} explanation - شرح الإجابة (اختياري).
 */
const subQuestionSchema = new Schema({
    question_text: { type: String, required: true, trim: true },
    options: {
        A: { type: simpleOptionSchema, required: true },
        B: { type: simpleOptionSchema, required: true },
        C: { type: simpleOptionSchema, required: true },
        D: { type: simpleOptionSchema, required: true },
    },
    correct_option: { type: String, required: true, enum: ['A', 'B', 'C', 'D'] },
    difficulty_level: { type: Number, min: 1, max: 5 },
    taqfeelah: { type: Boolean, default: false },
    notes: { type: String, trim: true },
    explanation: { type: String, trim: true },
});

/**
 * `readingSchema` (نموذج سؤال فهم المقروء).
 * يحدد بنية أسئلة فهم المقروء، التي تتكون من مقطع نصي ومجموعة من الأسئلة الفرعية.
 * 
 * @property {string} passage - نص المقطع (إلزامي، يتم إزالة المسافات البيضاء الزائدة).
 * @property {Array<object>} questions - مصفوفة من الأسئلة الفرعية باستخدام `subQuestionSchema`.
 */
const readingSchema = new Schema({
    passage: { type: String, required: true, trim: true },
    questions: [subQuestionSchema], // مصفوفة من الأسئلة الفرعية
});

// تعريف أنواع الأسئلة المشتقة (Discriminators)
// تسمح هذه الميزة بتخزين أنواع مختلفة من المستندات في نفس المجموعة
// مع وجود حقول خاصة لكل نوع، مع الحفاظ على الحقول المشتركة من النموذج الأساسي.

// سؤال الإكمال (Completion Question)
const CompletionQuestion = Question.discriminator('completion', simpleChoiceSchema);
// سؤال اكتشاف الخطأ (Error Question)
const ErrorQuestion = Question.discriminator('error', errorQuestionSchema);
// سؤال التشبيه (Analogy Question)
const AnalogyQuestion = Question.discriminator('analogy', complexChoiceSchema);
// سؤال الشاذ (Odd One Out Question)
const OddOneOutQuestion = Question.discriminator('odd_one_out', complexChoiceSchema);
// سؤال فهم المقروء (Reading Comprehension Question)
const ReadingQuestion = Question.discriminator('reading', readingSchema);

// تصدير جميع النماذج لاستخدامها في أجزاء أخرى من التطبيق
module.exports = {
    Question, // النموذج الأساسي
    CompletionQuestion,
    ErrorQuestion,
    AnalogyQuestion,
    OddOneOutQuestion,
    ReadingQuestion,
};