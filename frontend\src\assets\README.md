# أصول الواجهة الأمامية (Frontend Assets)

يحتوي هذا المجلد على الأصول الثابتة (static assets) المستخدمة في الواجهة الأمامية للتطبيق. تشمل هذه الأصول عادةً الصور، الأيقونات، الخطوط، أو أي ملفات أخرى لا تتغير ديناميكيًا وتستخدم لدعم العرض البصري للتطبيق.

## هيكل المجلد

يتكون المجلد حاليًا من ملف SVG واحد.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `react.svg`
أيقونة SVG تمثل شعار React. تُستخدم عادةً لأغراض العرض أو كجزء من واجهة المستخدم.
