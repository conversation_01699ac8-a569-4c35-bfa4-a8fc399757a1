/**
 * مكون WelcomeHeader (رأس الترحيب).
 * يعرض رسالة ترحيب شخصية للمستخدم مع صورة رمزية.
 * 
 * @param {Object} props - خصائص المكون
 * @param {Object} props.user - بيانات المستخدم
 * @returns {JSX.Element} - مكون رأس الترحيب
 */
const WelcomeHeader = ({ user }) => {
  return (
    <section className="dashboard-header">
      <div className="welcome-content">
        <div className="welcome-text">
          <h1 className="dashboard-title">
            مرحباً بك مرة أخرى، {user?.fullName || user?.username || 'طالب'}! 👋
          </h1>
          <p className="dashboard-subtitle">
            هل أنت مستعد لمواصلة رحلتك التعليمية؟ دعنا نختبر معرفتك ونتتبع تقدمك.
          </p>
        </div>
        <div className="user-avatar">
          <div className="avatar-circle">
            {(user?.fullName || user?.username || 'S').charAt(0).toUpperCase()}
          </div>
        </div>
      </div>
    </section>
  );
};

export default WelcomeHeader;
