# إعدادات الواجهة الأمامية (Frontend Configuration)

يحتوي هذا المجلد على ملفات الإعدادات والتكوينات الخاصة بالواجهة الأمامية للتطبيق. هذه الملفات ضرورية لتهيئة الخدمات الخارجية أو السلوكيات العامة للتطبيق.

## هيكل المجلد

يتكون المجلد حاليًا من ملف JavaScript واحد يختص بإعدادات مراقبة الأخطاء.

## قائمة الملفات

فيما يلي وصف لكل ملف داخل هذا المجلد:

### `sentry.js`
يقوم هذا الملف بتهيئة مكتبة Sentry لمراقبة الأخطاء والأداء في الواجهة الأمامية. يتضمن إعدادات DSN، البيئة، معدلات أخذ العينات للتتبع وإعادة تشغيل الجلسات، بالإضافة إلى تكاملات Sentry مع React Router. كما يحتوي على منطق تصفية متقدم للأخطاء لتقليل الضوضاء في تقارير Sentry، ويوفر دوال مساعدة لالتقاط الأخطاء والرسائل مع سياق إضافي.
