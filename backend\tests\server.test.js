const request = require('supertest');
const app = require('../app'); // استيراد التطبيق من ملف app.js الجديد

/**
 * مجموعة اختبارات `Server Health Checks`.
 * تختبر صحة عمل الخادم ونقاط النهاية الأساسية.
 */
describe('اختبارات صحة الخادم', () => {

    /**
     * اختبار: يجب أن يعيد 200 OK ورسالة ترحيب للمسار الجذر '/'.
     * يتحقق من أن الخادم يستجيب بشكل صحيح للطلبات على المسار الأساسي.
     */
    test('يجب أن يعيد 200 OK ورسالة ترحيب للمسار الجذر', async () => {
        // نرسل طلب GET إلى المسار '/'
        const res = await request(app).get('/');

        // نتوقع أن تكون حالة الاستجابة 200
        expect(res.statusCode).toEqual(200);
        // نتوقع أن يكون نص الاستجابة هو رسالة الترحيب المحددة
        expect(res.text).toBe('Hello from Backend! Connection successful.');
    });

    /**
     * اختبار: يجب أن يعيد 200 OK وحالة 'UP' لمسار فحص الصحة '/health'.
     * يتحقق من أن نقطة نهاية فحص الصحة تعمل بشكل صحيح.
     */
    test('يجب أن يعيد 200 OK وحالة UP لمسار /health', async () => {
        // نرسل طلب GET إلى المسار '/health'
        const res = await request(app).get('/health');

        // نتوقع أن تكون حالة الاستجابة 200
        expect(res.statusCode).toEqual(200);
        // نتوقع أن يحتوي جسم الاستجابة على خاصية 'status' بقيمة 'UP'
        expect(res.body.status).toBe('UP');
        // نتوقع أن يحتوي جسم الاستجابة على خاصية اسمها 'timestamp'
        expect(res.body).toHaveProperty('timestamp');
    });

});
