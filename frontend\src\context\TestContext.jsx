// استيراد هوكس React الأساسية لإنشاء السياق وإدارة الحالة
import React, { createContext, useState, useContext } from 'react';

// إنشاء سياق الاختبار (TestContext)
// سيتم استخدام هذا السياق لتوفير حالة الاختبار ووظائفه لجميع المكونات الفرعية.
export const TestContext = createContext(null);

/**
 * مكون `TestProvider`.
 * يوفر سياق الاختبار لجميع المكونات الفرعية.
 * يدير حالة أسئلة الاختبار ونتائج الاختبار.
 * 
 * @param {object} props - خصائص المكون.
 * @param {React.ReactNode} props.children - المكونات الفرعية التي ستتمكن من الوصول إلى سياق الاختبار.
 * @returns {JSX.Element} - مزود سياق الاختبار.
 */
export const TestProvider = ({ children }) => {
  // حالة `testQuestions`: مصفوفة لتخزين أسئلة الاختبار الحالية.
  const [testQuestions, setTestQuestions] = useState([]);
  // حالة `testResult`: لتخزين نتيجة الاختبار (مثل الدرجة، الإجابات الصحيحة/الخاطئة).
  const [testResult, setTestResult] = useState(null);

  /**
   * دالة `startTest`.
   * تبدأ جلسة اختبار جديدة عن طريق تعيين الأسئلة ومسح أي نتائج سابقة.
   * 
   * @param {Array<object>} questions - مصفوفة من كائنات الأسئلة للاختبار الجديد.
   */
  const startTest = (questions) => {
    setTestQuestions(questions); // تعيين أسئلة الاختبار
    setTestResult(null); // مسح أي نتائج اختبار سابقة
  };

  // الكائن الذي سيتم توفيره عبر سياق الاختبار للمكونات الفرعية
  const value = {
    testQuestions,
    startTest,
    testResult,
    setTestResult,
  };

  return (
    // توفير الكائن `value` لجميع المكونات الفرعية
    <TestContext.Provider value={value}>
      {children}
    </TestContext.Provider>
  );
};

/**
 * هوك مخصص (Custom Hook) `useTest`.
 * يوفر طريقة سهلة للوصول إلى سياق الاختبار من أي مكون وظيفي.
 * 
 * @returns {object} - كائن يحتوي على حالة الاختبار ودواله.
 * @throws {Error} - يرمي خطأ إذا لم يتم استخدام الهوك داخل مكون `TestProvider`.
 */
export const useTest = () => {
  const context = useContext(TestContext);
  // التحقق للتأكد من أن الهوك يستخدم داخل مزود السياق
  if (!context) {
    throw new Error('يجب استخدام useTest داخل مكون TestProvider');
  }
  return context;
};
