// استيراد مكتبة React
import React from 'react';
// استيراد ملف التنسيقات المشترك الذي يحتوي على أنماط مفتاح التبديل
import './Navbar.css';

/**
 * مكون ThemeSwitcher (مفتاح تبديل المظهر).
 * يوفر واجهة مستخدم لتبديل مظهر التطبيق بين الوضع الفاتح والداكن.
 * يعتمد على تنسيقات CSS المعرفة في `Navbar.css` لشكله ووظيفته البصرية.
 * 
 * ملاحظة: هذا المكون يعرض فقط واجهة المستخدم لمفتاح التبديل.
 * منطق تبديل المظهر الفعلي (مثل تحديث سمة البيانات في DOM)
 * يجب أن يتم التعامل معه في مكان آخر (على الأرجح في `ThemeContext.jsx`).
 * 
 * @returns {JSX.Element} - مفتاح تبديل المظهر.
 */
const ThemeSwitcher = () => {
  return (
    // الحاوية الرئيسية لمفتاح التبديل، تستخدم تنسيقات `.ui-switch` من `Navbar.css`
    <label className="ui-switch">
      {/* مربع الاختيار المخفي الذي يتحكم في حالة التبديل (تشغيل/إيقاف) */}
      <input type="checkbox" />
      {/* العنصر البصري للمفتاح المنزلق */}
      <div className="slider">
        {/* الدائرة المتحركة داخل المفتاح المنزلق */}
        <div className="circle"></div>
      </div>
    </label>
  );
};

// تصدير المكون ليكون متاحًا للاستخدام في أجزاء أخرى من التطبيق
export default ThemeSwitcher;
