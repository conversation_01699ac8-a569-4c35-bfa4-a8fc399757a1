// استيراد هوكس React لإدارة الحالة ودورة حياة المكونات
import { useState, useEffect, useContext } from 'react';
// استيراد هوكس React Router للتنقل وإنشاء الروابط
import { useNavigate } from 'react-router-dom';
// استيراد هوك useAuth للوصول إلى بيانات المستخدم وحالة المصادقة
import { useAuth } from '../context/AuthContext';
// استيراد سياق الاختبار للوصول إلى دالة بدء الاختبار
import { TestContext } from '../context/TestContext';
// استيراد دالة Sentry لالتقاط الأخطاء مع السياق
import { captureErrorWithContext } from '../config/sentry';
// استيراد دوال API لإجراء طلبات مصادق عليها (POST, GET)
import { authenticatedPost, authenticatedGet } from '../utils/api';
// استيراد المكونات الفرعية الجديدة
import WelcomeHeader from '../components/WelcomeHeader';
import QuickActions from '../components/QuickActions';
import UserStats from '../components/UserStats';

/**
 * مكون DashboardPage (صفحة لوحة التحكم).
 * يعرض لوحة تحكم شخصية للمستخدم تتضمن رسالة ترحيب،
 * خيارات سريعة مثل بدء اختبار جديد، وإحصائيات أداء المستخدم.
 * 
 * @returns {JSX.Element} - صفحة لوحة التحكم.
 */
const DashboardPage = () => {
  // حالة لتتبع ما إذا كان الاختبار قيد البدء
  const [isStartingTest, setIsStartingTest] = useState(false);
  // حالة لتخزين رسائل الخطأ
  const [error, setError] = useState('');
  // حالة لتخزين إحصائيات أداء المستخدم
  const [userStats, setUserStats] = useState(null);

  // الحصول على بيانات المستخدم من سياق المصادقة
  const { user } = useAuth();
  // الحصول على دالة بدء الاختبار من سياق الاختبار
  const { startTest } = useContext(TestContext);
  // هوك للتنقل بين المسارات
  const navigate = useNavigate();

  // تأثير جانبي (useEffect) لجلب بيانات لوحة التحكم (إحصائيات المستخدم)
  useEffect(() => {
    const loadDashboardData = async () => {
      // لا يتم جلب البيانات إذا لم يكن هناك توكن للمستخدم
      if (!user?.token) return;
      try {
        // إرسال طلب GET مصادق عليه لجلب إحصائيات المستخدم
        const statsData = await authenticatedGet('/api/users/stats', user.token, {}, {
          component: 'DashboardPage',
          operation: 'loadStats'
        });
        setUserStats(statsData); // تحديث حالة إحصائيات المستخدم
      } catch (err) {
        console.warn('فشل تحميل إحصائيات لوحة التحكم:', err.message);
        // في حالة الفشل، يتم إعادة تعيين الإحصائيات لتجنب عرض بيانات قديمة
        setUserStats({ totalTests: 0, averageScore: 0, bestScore: 0 });
      }
    };
    loadDashboardData(); // استدعاء الدالة عند تحميل المكون أو تغير توكن المستخدم
  }, [user?.token]); // يعتمد على توكن المستخدم لإعادة الجلب عند تغيره

  /**
   * دالة `handleStartTest`.
   * تبدأ عملية اختبار جديدة عن طريق إرسال طلب إلى الخادم.
   */
  const handleStartTest = async () => {
    // التحقق من مصادقة المستخدم قبل بدء الاختبار
    if (!user?.token) {
      setError('المصادقة مطلوبة لبدء الاختبار.');
      return;
    }
    setIsStartingTest(true); // تعيين حالة بدء الاختبار إلى true
    setError(''); // مسح أي أخطاء سابقة
    try {
      // إرسال طلب POST مصادق عليه لبدء اختبار جديد (طلب 10 أسئلة)
      const data = await authenticatedPost('/api/tests/start', user.token, { questionCount: 10 });
      startTest(data.questions); // تعيين الأسئلة في سياق الاختبار
      navigate(`/testing/${data.sessionId}`); // الانتقال إلى صفحة الاختبار
    } catch (err) {
      setError(err.message || 'فشل بدء الاختبار'); // عرض رسالة الخطأ
      captureErrorWithContext(err); // التقاط الخطأ وإرساله إلى Sentry
    } finally {
      setIsStartingTest(false); // إعادة تعيين حالة بدء الاختبار
    }
  };

  return (
    <div className="dashboard-modern">
      {/* مكون رأس الترحيب */}
      <WelcomeHeader user={user} />

      {/* مكون الإجراءات السريعة */}
      <QuickActions
        handleStartTest={handleStartTest}
        isStartingTest={isStartingTest}
        error={error}
      />

      {/* مكون إحصائيات المستخدم */}
      <UserStats userStats={userStats} />
    </div>
  );
};

// تصدير المكون ليكون متاحًا للاستخدام في أجزاء أخرى من التطبيق
export default DashboardPage;
